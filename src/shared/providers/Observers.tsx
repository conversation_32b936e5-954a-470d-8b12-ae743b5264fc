'use client';

import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef } from 'react';
import { toast as reactToastify } from 'react-toastify';
import useGetPagePlanInfo from '@shared/hooks/api-hook/useGetPagePlanInfo';
import useFirebaseClient from 'shared/hooks/useFirebaseClient';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import useOnlineStatus from 'shared/hooks/useOnlineStatus';
import useWebSocketConfig from 'shared/hooks/useWebSocketConfig';
import useToast from 'shared/uikit/Toast/useToast';
import useTheme from 'shared/uikit/utils/useTheme';
import useGetDeviceId from 'shared/utils/hooks/useGetDeviceId';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Cookies from 'shared/utils/toolkit/cookies';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';

const Observer = () => {
  useWebSocketConfig();
  useGetDeviceId();
  useFirebaseClient();
  useGetPagePlanInfo();
  const toastId = React.useRef<any>(null);
  const { showError } = useOnlineStatus();
  const searchParams = useSearchParams();
  const addedAccountForPeopleInvitation = searchParams.get(
    'addedAccountForPeopleInvitation'
  );
  const isError = searchParams.get('isError');
  const toast = useToast();
  const { t } = useTranslation();
  const isErrorShown = useRef<boolean>(false);
  const { changeTheme } = useTheme();

  useEffect(() => {
    if (isErrorShown.current) return;
    if (addedAccountForPeopleInvitation && !isError) {
      openMultiStepForm({
        formName: 'invitePeople',
        data: {
          initialMethod: 'google',
        },
      });
    } else if (addedAccountForPeopleInvitation && isError) {
      isErrorShown.current = true;
      openMultiStepForm({
        formName: 'invitePeople',
        data: {
          initialMethod: 'google',
        },
      });
      toast({
        type: 'error',
        icon: 'times-circle',
        title: t('acc_sync_failed'),
        message: t('try_again_to_add_your_google_account_make_sure_checkboxes'),
        autoClose: false,
      });
      setTimeout(() => {
        isErrorShown.current = false;
      }, 2000);
    }
  }, [addedAccountForPeopleInvitation]);

  useEffect(() => {
    const IS_DARK_KEY = getCookieKey('isDark');
    const cookieIsDark = Cookies.get(IS_DARK_KEY);
    const browserIsDark = window.matchMedia(
      '(prefers-color-scheme: dark)'
    ).matches;
    if (browserIsDark && typeof cookieIsDark === 'undefined') {
      changeTheme();
    }
  }, []);

  useEffect(() => {
    if (showError) {
      toastId.current = toast({
        id: 'NETWORK_ERROR',
        icon: 'wifi-slash',
        title: t('NETWORK_ERROR'),
        message: t('pls_check_y_int_connec'),
        autoClose: false,
      });
    } else {
      reactToastify.dismiss(toastId.current);
    }
  }, [showError]);

  return null;
};

export default Observer;
