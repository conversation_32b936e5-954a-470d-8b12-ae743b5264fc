import TextLink from '@shared/uikit/Link/TextLink';
import { routeNames } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import classes from './ActivityStyles.module.scss';
import type { ActivityProps, ActivityType } from '@shared/types/activityProps';
import type { FC } from 'react';

interface ActivityTitleBoxProps {
  item: ActivityProps;
}

const ActivityTitleBox: FC<ActivityTitleBoxProps> = ({ item }) => {
  const { t } = useTranslation();

  const titleInfo = titleText(item);
  const labels = titleInfo?.[item.type]?.words?.map((i) => i.label);
  const links = titleInfo?.[item.type]?.words?.map((i) => i.link);

  return (
    <ParseTextStringCP
      textProps={{ className: classes.title }}
      textString={translateReplacer(
        t(titleInfo?.[item.type]?.text ?? ''),
        labels
      )}
      tagComponentMap={{
        // eslint-disable-next-line react/no-unstable-nested-components
        0: (text) => <TextLink to={links[0]}>{text}</TextLink>,
        // eslint-disable-next-line react/no-unstable-nested-components
        1: (text) => <TextLink to={links[1]}>{t(text)}</TextLink>,
        // eslint-disable-next-line react/no-unstable-nested-components
        2: (text) => <TextLink to={links[2]}>{text}</TextLink>,
      }}
    />
  );
};

export default ActivityTitleBox;

const titleText: (activity: ActivityProps) => {
  [key in ActivityType]?: { text: string; words?: string[] };
} = (activity) => {
  const userFullName = activity.user.fullName;
  const userLink = `/${activity.user.username}`;
  const jobName = activity.job?.title ?? 'removed_job';
  const jobLink = `${routeNames.searchRecruiterJobs}?currentEntityId=${activity.job?.title}`;
  const job = { label: jobName, link: jobLink };
  const secondUser = activity.applicantUser
    ? {
        label: activity.applicantUser.profile?.fullName,
        link: `/${activity.applicantUser.profile?.username}`,
      }
    : {
        label: activity.candidateUser?.profile?.fullName,
        link: `/${activity.candidateUser?.profile?.username}`,
      };

  return {
    USER_APPLIED: {
      text: 'user_applied_activity',
      words: [{ label: userFullName, link: userLink }],
    },
    USER_WITHDRAWN: {
      text: 'user_withdraw_activity',
      words: [{ label: userFullName, link: userLink }],
    },
    PIPELINE_CHANGED: {
      text: 'pipeline_changed_activity',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    CANDIDATE_RELINKED: {
      text: 'activity_candidate_reselected',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    CANDIDATE_REJECTED: {
      text: 'activity_candidate_rejected',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    CANDIDATE_REMOVED: {
      text: 'activity_candidate_removed',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    CANDIDATE_SELECTED: {
      text: 'activity_candidate_selected',
      words: [{ label: userFullName, link: userLink }, secondUser, job],
    },
    CANDIDATE_SUBMITTED: {
      text: 'activity_candidate_submitted',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    CANDIDATE_WITHDRAWN: {
      text: 'activity_candidate_withdrawn',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    REVIEW_ADDED: {
      text: 'review_added_activity',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    REVIEW_MODIFIED: {
      text: 'review_modified_activity',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    REVIEW_REMOVED: {
      text: 'review_removed_activity',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    NOTE_ADDED: {
      text: 'activity_note_added',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    NOTE_MODIFIED: {
      text: 'activity_note_modified',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    NOTE_REMOVED: {
      text: 'activity_note_removed',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    JOB_STATUS_CHANGED: {
      text: 'job_status_changed',
      words: [{ label: userFullName, link: userLink }, job],
    },
    JOB_PRIORITY_CHANGED: {
      text: 'job_priority_changed',
      words: [{ label: userFullName, link: userLink }, job],
    },
    JOB_UPDATED: {
      text: 'activity_job_updated',
      words: [{ label: userFullName, link: userLink }, job],
    },
    JOB_SUBMITTED_AS_CLIENT: {
      text: 'activity_job_submitted_as_client',
      words: [{ label: userFullName, link: userLink }, job],
    },
    JOB_WITHDRAWN_AS_CLIENT: {
      text: 'activity_job_withdrawn_as_client',
      words: [{ label: userFullName, link: userLink }, job],
    },

    PROJECT_UPDATED: {
      text: 'activity_project_updated',
      words: [
        { label: userFullName, link: userLink },
        activity.project?.title || '',
      ],
    },
    COLLABORATOR_ADDED_TO_PROJECT: {
      text: 'activity_assignee_added',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    COLLABORATOR_REMOVED_FROM_PROJECT: {
      text: 'activity_assignee_removed',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    COLLABORATOR_ADDED: {
      text: 'activity_collaborator_added',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    COLLABORATOR_REMOVED: {
      text: 'activity_collaborator_removed',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    JOB_ADDED_TO_PROJECT: {
      text: 'activity_job_linked',
      words: [{ label: userFullName, link: userLink }, job],
    },
    JOB_REMOVED_FROM_PROJECT: {
      text: 'activity_job_unlinked',
      words: [{ label: userFullName, link: userLink }, job],
    },
    TODO_ADDED: {
      text: 'activity_todo_added',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    TODO_MODIFIED: {
      text: 'activity_todo_modified',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    TODO_REMOVED: {
      text: 'activity_todo_removed',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    MEETING_SCHEDULED: {
      text: 'activity_meeting_scheduled',
      words: [{ label: userFullName, link: userLink }, secondUser, job],
    },
    SCOREBOARD_UPDATED: {
      text: 'activity_scoreboard_updated',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    DOCUMENT_UPDATED: {
      text: 'activity_document_updated',
      words: [{ label: userFullName, link: userLink }, secondUser],
    },
    VENDOR_CLIENT_ADDED: {
      text: 'activity_vendor_client_added',
      words: [{ label: userFullName, link: userLink }],
    },
    VENDOR_REMOVED: {
      text: 'activity_vendor_removed',
      words: [{ label: userFullName, link: userLink }],
    },
    CLIENT_REMOVED: {
      text: 'activity_client_removed',
      words: [{ label: userFullName, link: userLink }],
    },
  };
};
