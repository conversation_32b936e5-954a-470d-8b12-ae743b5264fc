import React, { useCallback } from 'react';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PreferencesModal from 'shared/components/Organism/PreferencesModal/PreferencesModal';
import SettingsContent from 'shared/components/Organism/SettingsModal/SettingsContent.component';
import urls from 'shared/constants/urls';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import {
  useGlobalDispatch,
  useGlobalState,
} from 'shared/contexts/Global/global.provider';
import useHasPermission from 'shared/hooks/useHasPermission';
import { useProfilePanelState } from 'shared/hooks/useSettingsState';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import Switch from 'shared/uikit/Switch';
import cnj from 'shared/uikit/utils/cnj';
import Typography from 'shared/uikit/Typography';
import useMedia from 'shared/uikit/utils/useMedia';
import useTheme from 'shared/uikit/utils/useTheme';
import { routeNames } from 'shared/utils/constants/routeNames';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useHistory from 'shared/utils/hooks/useHistory';
import useTranslation from 'shared/utils/hooks/useTranslation';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import classes from './SettingsPage.module.scss';

const DARK_MODE = 'dar_mode';

interface SettingsProps {
  onGoBack: VoidFunction;
  closeProfilePanel: VoidFunction;
}

const Settings = (props: SettingsProps) => {
  const { onGoBack, closeProfilePanel } = props;
  const { state, setProfilePanelState } = useProfilePanelState();
  const isPreferencesModalOpen = !!state?.settings?.preferences;
  const history = useHistory();
  const appDispatch = useGlobalDispatch();
  const { t } = useTranslation();
  const { isDark, changeTheme } = useTheme();
  const isFeedbackModalOpen = useGlobalState('isFeedbackModalOpen');

  const changeThemeHandler = () => {
    changeTheme();
  };
  const openFeedbackModal = useCallback(() => {
    if (!isFeedbackModalOpen) {
      appDispatch({ type: 'TOGGLE_FEEDBACK_MODAL' });
    }
  }, [appDispatch, isFeedbackModalOpen]);

  const openPreferencesModal = useCallback(() => {
    setProfilePanelState({ settings: { preferences: {} } });
  }, [setProfilePanelState]);

  const closePreferencesModal = useCallback(() => {
    setProfilePanelState({ settings: {} });
  }, [setProfilePanelState]);

  const changeThemeAction = (
    <Switch value={isDark} className={classes.switch} />
  );

  const settings = isBusinessApp
    ? [
        {
          title: 'page_details',
          subTitle: 'name_username_l',
          icon: 'id-card',
          routeName: routeNames.settingsPageDetail,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'account',
          subTitle: 'account_type_delete',
          icon: 'user-circle',
          routeName: routeNames.settingsPageAccount,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'accessibility',
          subTitle: 'mgn_owner_page_role',
          icon: 'user-cog',
          routeName: routeNames.settingsPageRoles,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'privacy',
          subTitle: 'visi_block_user',
          icon: 'eye-slash',
          routeName: routeNames.settingsPagePrivacy,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'help_support',
          subTitle: 'faq_privacy_policy',
          icon: 'question-circle',
        },
        {
          title: 'feedback',
          subTitle: 'contact_us',
          icon: 'comment-alt-edit',
          onClick: openFeedbackModal,
        },
        {
          title: DARK_MODE,
          subTitle: isDark ? 'turn_on_l' : 'turn_off_l',
          icon: isDark ? 'lightbulb' : 'lightbulb-slash',
          onClick: changeThemeHandler,
          rightAction: changeThemeAction,
          doNotCloseAfterClick: true,
        },
      ]
    : [
        {
          title: 'profile_details',
          subTitle: 'name_username_l',
          icon: 'id-card',
          routeName: routeNames.settingsProfileDetail,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'account',
          subTitle: 'account_type_delete',
          icon: 'user-circle',
          routeName: routeNames.settingsAccount,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'privacy',
          subTitle: 'visi_block_user',
          icon: 'eye-slash',
          routeName: routeNames.settingsPrivacy,
          scopes: [SCOPES.canSeeSettingsScreen],
        },
        {
          title: 'security_login',
          subTitle: 'password_l_i_d',
          icon: 'shield-check',
          routeName: routeNames.settingsSecurity,
        },
        {
          title: 'help_support',
          subTitle: 'faq_privacy_policy',
          icon: 'question-circle',
        },
        {
          title: 'preferences',
          subTitle: 'sub_preferences',
          icon: 'user-cog',
          onClick: openPreferencesModal,
          doNotCloseAfterClick: true,
        },
        {
          title: 'support',
          subTitle: 'contact_us',
          icon: 'comment-alt-edit',
          onClick: openFeedbackModal,
        },
        {
          title: DARK_MODE,
          subTitle: isDark ? 'turn_on_l' : 'turn_off_l',
          icon: isDark ? 'lightbulb' : 'lightbulb-slash',
          onClick: changeThemeHandler,
          rightAction: changeThemeAction,
          doNotCloseAfterClick: true,
        },
      ];

  const onItemClickHandler = (item: any) => {
    if (item.title === 'help_support') {
      window.location.href = urls.helpAndSupport;
    } else if (item.onClick) {
      item.onClick();
    } else {
      history.push(item.routeName);
    }
    if (!item?.doNotCloseAfterClick) {
      closeProfilePanel();
    }
  };

  const { isMoreThanTablet } = useMedia();

  const seeAllHandler = () => {
    closeProfilePanel();

    history.push(
      isMoreThanTablet
        ? isBusinessApp
          ? routeNames.settingsPageDetail
          : routeNames.settingsProfileDetail
        : routeNames.settings
    );
  };

  if (isPreferencesModalOpen) {
    return <PreferencesModal onBack={closePreferencesModal} />;
  }

  return (
    <FixedRightSideModalDialog onClickOutside={closeProfilePanel}>
      <Flex className={classes.root}>
        <ModalHeaderSimple
          backButtonProps={{ onClick: onGoBack }}
          title={t('settings')}
          visibleHeaderDivider
          rightContent={() => (
            <>
              <Flex className={classes.filler} />
              <PermissionsGate scopes={[SCOPES.canSeeSettingsScreen]}>
                <BaseButton onClick={seeAllHandler}>
                  <Typography
                    color="brand"
                    className={classes.subTitle}
                    font="700"
                  >
                    {t('see_all')}
                  </Typography>
                </BaseButton>
              </PermissionsGate>
            </>
          )}
          noCloseButton
          hideBack={false}
        />

        <Flex className={classes.contentWrapper}>
          <SettingsContent
            items={settings}
            onItemClickHandler={onItemClickHandler}
          />
        </Flex>
      </Flex>
    </FixedRightSideModalDialog>
  );
};

export default Settings;
