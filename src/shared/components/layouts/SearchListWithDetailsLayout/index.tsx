import dynamic from 'next/dynamic';
import React, { useEffect, useRef, useState } from 'react';
import SearchFiltersModalSkeleton from '@shared/components/Organism/SearchFiltersModal/SearchFiltersModal.skeleton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import ResponsiveGlobalSearch from 'shared/components/molecules/ResponsiveGlobalSearch';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import {
  useSearchDispatch,
  useSearchState,
} from 'shared/contexts/search/search.provider';
import Flex from 'shared/uikit/Flex';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import cnj from 'shared/uikit/utils/cnj';
import { makeDynamicStyles } from 'shared/uikit/utils/makeDynamicStyles';
import useMedia from 'shared/uikit/utils/useMedia';
import useHistory from 'shared/utils/hooks/useHistory';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './index.module.scss';
import SearchFilters, { type SearchFiltersProps } from './SearchFilters';
import type { FC } from 'react';

const SearchFiltersModal = dynamic(
  () => import('../../Organism/SearchFiltersModal'),
  { ssr: false }
);

export interface SearchListWithDetailsLayoutProps {
  title?: string;
  isEmpty?: boolean;
  listComponent: React.ReactElement;
  detailsComponent?: React.ReactElement;
  onBackHandler?: () => void;
  isLoading?: boolean;
  groups?: any;
  classNames?: {
    contentRoot?: string;
    list?: string;
    headerRoot?: string;
    detailsRoot?: string;
    emptyState?: string;
    rightWrapper?: string;
    filtersWrapper?: string;
  };
  headerComponents?: React.ReactNode;
  hasBackBtn?: boolean;
  isTotalyEmpty?: boolean;
  sectionTotalEmpty?: React.ReactNode;
  hasFilterIcon?: boolean;
  FiltersComponent?: FC<SearchFiltersProps>;
  filterTopComponent?: React.ReactElement | undefined;
  isFullWidth?: boolean;
  headerHeight?: number;
  searchInputDefaultView?: 'input' | 'text';
  listTopComponent?: React.ReactElement | undefined;
  SearchFiltersHeaderComponent?: React.FC<any>;
}

const SearchListWithDetailsLayout: React.FC<
  SearchListWithDetailsLayoutProps
> = ({
  isLoading,
  title,
  isEmpty,
  detailsComponent,
  listComponent,
  groups,
  classNames,
  headerComponents,
  hasBackBtn = true,
  isTotalyEmpty,
  sectionTotalEmpty,
  hasFilterIcon = true,
  filterTopComponent,
  FiltersComponent = SearchFilters,
  isFullWidth,
  headerHeight = 56,
  listTopComponent,
  searchInputDefaultView = 'input',
  SearchFiltersHeaderComponent,
}) => {
  const isLoggedIn = useAuthState('isLoggedIn');
  const history = useHistory();
  const intoViewRef = useRef<HTMLElement>(null);
  const listTopComponentRef = useRef<HTMLDivElement>(null);
  const [listTopComponentHeight, setListTopComponentHeight] = useState(0);
  const { t } = useTranslation();
  const searchDispatch = useSearchDispatch();
  const isSearchFilterFormOpen = useSearchState('isSearchFilterFormOpen');
  const onBackHandler = () => history.goBack();
  const emptyMsg = t('try_refining_search');
  const { isTabletAndLess } = useMedia();
  const toggleFilterModal = (payload: boolean) => {
    searchDispatch({ type: 'SET_IS_SEARCH_FILTER_FORM_OPEN', payload });
  };
  const closeFilterHandler = () => toggleFilterModal(false);

  useEffect(() => {
    if (isSearchFilterFormOpen) {
      closeFilterHandler();
    }
  }, []);

  useEffect(() => {
    if (listTopComponent && listTopComponentRef.current) {
      const height = listTopComponentRef.current.offsetHeight;
      setListTopComponentHeight(height);
    } else {
      setListTopComponentHeight(0);
    }
  }, [listTopComponent]);

  return (
    <>
      {isLoggedIn && (
        <>
          <ModalHeaderSimple
            backButtonProps={{
              className: classes.mobileHeaderBackButton,
              onClick: onBackHandler,
            }}
            visibleHeaderDivider
            className={cnj(classes.mobileHeader)}
            title={title}
            rightContent={() => (
              <ResponsiveGlobalSearch
                iconViewClassName={classes.searchIon}
                defaultView={searchInputDefaultView}
              />
            )}
            belowContent={
              <FiltersComponent
                isLoading={isLoading}
                groups={groups}
                isTotalyEmpty={isTotalyEmpty}
                classNames={classNames}
                hasBackBtn={hasBackBtn}
                onBackHandler={onBackHandler}
                hasFilterIcon={hasFilterIcon}
                headerComponents={headerComponents}
                SearchFiltersHeaderComponent={SearchFiltersHeaderComponent}
              />
            }
          />
          <FiltersComponent
            isLoading={isLoading}
            groups={groups}
            isTotalyEmpty={isTotalyEmpty}
            classNames={{
              ...classNames,
              root: classes.root,
            }}
            hasBackBtn={hasBackBtn}
            onBackHandler={onBackHandler}
            hasFilterIcon={hasFilterIcon}
            headerComponents={headerComponents}
            isFullWidth={isFullWidth}
            filterTopComponent={filterTopComponent}
            SearchFiltersHeaderComponent={SearchFiltersHeaderComponent}
          />
        </>
      )}
      <Flex
        className={cnj(
          classes.contentRoot,
          !isFullWidth && classes.maxWidth,
          classNames?.contentRoot
        )}
        {...(isEmpty || isTabletAndLess
          ? {}
          : makeDynamicStyles({
              maxHeight: `calc(100% - ${isLoggedIn ? headerHeight + listTopComponentHeight : 20}px)`,
            }))}
      >
        {listTopComponent && (
          <div ref={listTopComponentRef}>{listTopComponent}</div>
        )}
        {isTotalyEmpty ? (
          sectionTotalEmpty
        ) : isEmpty ? (
          <EmptySectionInModules
            isInsideTab
            isFullHeight
            title={t('no_result_f')}
            text={emptyMsg}
            className={classNames?.emptyState}
          />
        ) : (
          <Flex className={classes.jobsListWithDetails}>
            <Flex
              className={cnj(
                classes.list,
                isFullWidth && classes.listFullWidth,
                classNames?.list
              )}
            >
              <Flex ref={intoViewRef} />
              {listComponent}
            </Flex>
            {detailsComponent && (
              <Flex
                className={cnj(
                  classes.details,
                  isFullWidth && classes.detailsFullWidth,
                  classNames?.detailsRoot
                )}
                id={PROFILE_SCROLL_WRAPPER}
              >
                {isTabletAndLess ? undefined : detailsComponent}
              </Flex>
            )}
          </Flex>
        )}
      </Flex>
      {isSearchFilterFormOpen && isLoggedIn && (
        <SearchFiltersModal
          onClose={closeFilterHandler}
          groups={groups}
          isFullWidth={isFullWidth}
        />
      )}
    </>
  );
};

export default SearchListWithDetailsLayout;
