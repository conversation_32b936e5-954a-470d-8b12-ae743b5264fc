import { JobItem } from '@shared/components/Organism/AppliedJobModal/JobItem';
import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import fuseSearch from '@shared/uikit/utils/fuseSearch';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React, { useState } from 'react';
import { JobProps } from '@shared/types/jobsProps';

const AppliedJobModal = () => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const appliedJobModalData = useGlobalState('appliedJobModalData');
  const [filteredJobs, setFilteredJobs] = useState<JobProps[]>(
    appliedJobModalData?.data?.jobs ?? []
  );
  const search = fuseSearch(appliedJobModalData?.data?.jobs ?? [], {
    keys: ['title'],
  });

  const onChangeInput = (input: string) => {
    const items = search(input);
    setFilteredJobs(items);
  };

  const handleClose = () =>
    appDispatch({
      type: 'TOGGLE_APPLIED_JOB_MODAL',
      payload: {
        open: false,
        data: undefined,
      },
    });

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple title={t('applied_jobs')} />
      <ModalBody className="!p-12">
        <SearchInputV2
          onChange={onChangeInput}
          placeholder={t('search')}
          className="my-12 px-4"
        />
        {filteredJobs?.map((job) => <JobItem key={job.id} job={job} />)}
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default AppliedJobModal;
