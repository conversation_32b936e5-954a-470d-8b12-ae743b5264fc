import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import { JobProps } from '@shared/types/jobsProps';
import IconButton from '@shared/uikit/Button/IconButton';
import { routeNames } from '@shared/utils/constants';
import React from 'react';

export const JobItem = ({ job }: { job: JobProps }) => {
  return (
    <ItemComponent
      image={job?.pageCroppedImageUrl}
      title={job?.title}
      subTitle={job?.projects?.map((project) => project?.title).join(', ')}
      subTitleIcon="projects-light"
    >
      <IconButton
        name="chevron-right"
        size="md20"
        color="brand"
        style={{ cursor: 'pointer' }}
        to={`${routeNames.searchRecruiterJobs}?currentEntityId=${job.id}`}
      />
    </ItemComponent>
  );
};
