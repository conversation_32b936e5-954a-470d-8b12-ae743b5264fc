import React from 'react';
import ParseTextStringCP from '@shared/components/molecules/TranslateReplacer';
import useGetPagePlanInfo from '@shared/hooks/api-hook/useGetPagePlanInfo';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import Button from '@shared/uikit/Button';
import MenuItem from '@shared/uikit/MenuItem';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useGetRole from 'shared/hooks/useGetRole';
import useHasPermission from 'shared/hooks/useHasPermission';
import Divider from 'shared/uikit/Divider';
import Flex from 'shared/uikit/Flex';
import { getPageAccessibilities } from 'shared/utils/api/page';
import { PAGE_MEMBER_STATUS } from 'shared/utils/constants/enums';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { getPortal } from 'shared/utils/getAppEnv';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import AccessibilityAdmins from './Accessibility.Admins';
import classes from './Accessibility.component.module.scss';
import AccessibilityMembers from './Accessibility.member';
import AccessibilityOwner from './Accessibility.owner';
import type { PageMemberType } from 'shared/hooks/api-hook/useGetPageMembers';

const Accessibility = (): JSX.Element => {
  const { t } = useTranslation();
  const { checkIsBusinessOwner } = useGetAppObject();
  const casAssignRole = useHasPermission([SCOPES.casAssignRole]);
  const { businessPage } = useGetAppObject();
  const { roles } = useGetRole();
  const pageId = businessPage?.id;
  const isMember = roles.includes('MEMBER');
  const { data: planInfo } = useGetPagePlanInfo();
  const {
    refetch,
    data: people = [],
    isLoading,
  } = useReactQuery<Array<PageMemberType>>({
    action: {
      spreadParams: true,
      apiFunc: getPageAccessibilities,
      key: [QueryKeys.getPageAccessibilities, pageId],
      params: {
        containsPendingAndDeclined: true,
      },
    },
  });

  const { members, owner, admins } = people.reduce(
    (prev, curr) => {
      if (checkIsBusinessOwner(curr.user.id)) {
        return { ...prev, owner: curr.user };
      }
      if (curr.pageMemberships.find((i) => i.role === 'ADMIN')) {
        return { ...prev, admins: [...prev.admins, curr] };
      }
      const member = curr.pageMemberships.find((i) => i.role === 'MEMBER');
      const portalAccesses = isMember
        ? curr.portalAccesses?.filter(
            (i) => i.portal === getPortal().toUpperCase()
          )
        : curr.portalAccesses;

      const item = {
        ...curr,
        portalAccesses,
        status: member?.status,
      };
      const existItem = prev.members.find((i) => i.userId === item.userId);
      const members =
        member?.status !== PAGE_MEMBER_STATUS.ACCEPTED && isMember
          ? [...prev.members]
          : [...prev.members, item];

      return {
        ...prev,
        members,
      };
    },
    { members: [], admins: [], owner: {} }
  );

  const adminOptions = members?.filter(
    (item) => item.status === PAGE_MEMBER_STATUS.ACCEPTED
  );

  const handleOpenPlans = () => {
    openNineDotPanel({
      isOpen: true,
      defaultActiveStep: 'PLANS',
    });
  };

  if (isLoading) return <></>;

  return (
    <Flex>
      <MenuItem
        title={
          <ParseTextStringCP
            textProps={{ color: 'secondaryDisabledText' }}
            textString={translateReplacer(
              t('purchased_seats_n_active_seats_m'),
              [
                planInfo?.activePlan?.noOfPurchaseSeat,
                planInfo?.activePlan?.noOfActiveSeat,
              ]
            )}
            tagComponentMap={{
              0: (text) => <Typography color="smoke_coal">{text}</Typography>,
              1: (text) => <Typography color="smoke_coal">{text}</Typography>,
            }}
          />
        }
        iconName="new-person"
        iconType="fal"
        iconSize={20}
        className="!bg-gray_5 !items-center"
        action={() => (
          <Button
            disabled={!casAssignRole}
            schema="transparent-brand"
            label={t('purchase_seats')}
            onClick={handleOpenPlans}
          />
        )}
      />
      <Flex className="mt-20">
        <AccessibilityMembers
          pageId={pageId}
          casAssignRole={casAssignRole}
          refetch={refetch}
          members={members}
        />
        {((casAssignRole && adminOptions.length > 0) || admins.length > 0) && (
          <>
            <Divider className={classes.divider} />
            <AccessibilityAdmins
              pageId={pageId}
              casAssignRole={casAssignRole}
              members={adminOptions}
              admins={admins}
              refetch={refetch}
            />
          </>
        )}
        <Divider className={classes.divider} />
        <AccessibilityOwner
          pageId={pageId}
          casAssignRole={casAssignRole}
          owner={owner}
          members={[...admins, ...adminOptions]}
        />
      </Flex>
    </Flex>
  );
};

export default Accessibility;
