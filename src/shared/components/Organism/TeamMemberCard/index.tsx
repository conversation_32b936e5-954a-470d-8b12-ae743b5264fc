import CandidateCard from '@shared/components/molecules/CandidateCard';
import CardBadges from '@shared/components/molecules/CardItem/CardBadges';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { ISearchTeamMembers } from '@shared/types/search';
import useOpenMessage from '@shared/hooks/useOpenMessage';
import { roomMemberRoles } from '@shared/components/Organism/Message/constants';
import { routeNames } from '@shared/utils/constants';

export default function TeamMemberCard({ item }: { item: ISearchTeamMembers }) {
  const { t } = useTranslation();

  const openMessage = useOpenMessage();
  const openMessageHandler = () => {
    openMessage({
      isGroupChat: false,
      id: item.id,
      icon: item.croppedImageUrl,
      name: item.fullName,
      owner: item.id,
      username: item.username,
      createdAt: new Date(),
      role: roomMemberRoles.Owner,
      isPage: false,
    });
  };

  return (
    <CandidateCard
      {...item}
      firstText={item?.fullName}
      secondText={item?.usernameAtSign}
      thirdText={item?.occupation?.label}
      fourthText={item?.location?.title}
      avatar={item?.croppedImageUrl}
      avatarProps={{ name: item?.fullName }}
      classNames={{
        root: '!border !border-solid !border-techGray_20 !rounded-xl',
      }}
    >
      <CardBadges
        notesCount={item?.notesCount}
        todosCount={item?.todosCount}
        meetingsCount={item?.meetingsCount}
        jobsCount={item?.jobsCount}
        candidatesCount={item?.candidatesCount}
        applicantsCount={item?.applicantsCount}
      />
      <Flex className="!flex-row gap-12">
        <Button
          label={t('message')}
          schema="semi-transparent"
          leftIcon="envelope"
          leftType="far"
          fullWidth
          onClick={openMessageHandler}
        />
        <Button
          label={t('view_details')}
          fullWidth
          to={`${routeNames.portalProfile}/${item.id}`}
        />
      </Flex>
    </CandidateCard>
  );
}
