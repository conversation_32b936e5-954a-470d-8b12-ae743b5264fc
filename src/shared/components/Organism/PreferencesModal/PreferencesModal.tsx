import React from 'react';
import { useProfilePanelState } from 'shared/hooks/useSettingsState';
import Flex from 'shared/uikit/Flex';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SchedulePreferencesModal from './PMSchedule';
import classes from './PreferencesModal.module.scss';
import PreferencesSingleItem from './PreferencesSingleItem';
import type { ComponentProps, FC } from 'react';
import cnj from '@shared/uikit/utils/cnj';

interface Props {
  onBack: () => void;
}

type Keys = undefined | 'schedule';

const subModalComponents: Record<Keys, any> = {
  schedule: SchedulePreferencesModal,
} as const;

const PreferencesModal: FC<Props> = ({ onBack }) => {
  const { state, previous, setProfilePanelState, closePanel } =
    useProfilePanelState();
  const opennedSubModal = Object?.keys?.(state?.settings?.preferences)?.[0];

  const { t } = useTranslation();

  const items: Array<ComponentProps<typeof PreferencesSingleItem>> = [
    {
      title: 'home_preferences',
      subtitle: 'preferences_subtitle_description',
    },
    {
      title: 'people_preferences',
      subtitle: 'preferences_subtitle_description',
    },
    {
      title: 'pages_preferences',
      subtitle: 'preferences_subtitle_description',
    },
    {
      title: 'schedule_preferences',
      subtitle: 'schedule_preferences_subtitle_description',
      onClick: () =>
        setProfilePanelState({ settings: { preferences: { schedule: {} } } }),
    },
    {
      title: 'message_preferences',
      subtitle: 'preferences_subtitle_description',
    },
    {
      title: 'jobs_preferences',
      subtitle: 'preferences_subtitle_description',
    },
  ];

  const onCloseProfilePanel = () => {
    closePanel();
  };

  if (opennedSubModal) {
    const SubModal = subModalComponents[opennedSubModal];

    return (
      <SubModal
        onBack={() =>
          previous
            ? setProfilePanelState(previous)
            : setProfilePanelState({ settings: { preferences: {} } })
        }
      />
    );
  }

  return (
    <FixedRightSideModalDialog onClickOutside={onCloseProfilePanel}>
      <Flex className={classes.root}>
        <ModalHeaderSimple
          noCloseButton
          hideBack={false}
          backButtonProps={{
            onClick: onBack,
          }}
          title={t('preferences')}
        />
        <Flex className={cnj(classes.body, 'gap-12')}>
          {items.map((item, idx) => (
            <PreferencesSingleItem key={`preferences_item_${idx}`} {...item} />
          ))}
        </Flex>
      </Flex>
    </FixedRightSideModalDialog>
  );
};

export default PreferencesModal;
