import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import Link from 'shared/uikit/Link';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './PreferencesSingleItem.module.scss';

interface Props {
  title: string;
  subtitle: string;
  onClick?: () => void;
  classNames?: {
    container: string;
  };
  routeName?: string;
}

const PreferencesSingleItem = ({
  title,
  subtitle,
  onClick,
  classNames,
  routeName,
}: Props): JSX.Element => {
  const { t } = useTranslation();
  const CP = routeName ? Link : BaseButton;
  const cpProps = routeName ? { to: routeName } : { onClick };

  return (
    <CP
      className={cnj(
        classes.headerItemWrapper,
        classNames?.container,
        'flex items-center justify-between hover:bg-hoverPrimary'
      )}
      {...cpProps}
    >
      <Flex className={classes.textWrapper}>
        <Flex className={classes.titleWrapper}>
          <Typography className="!text-base !font-bold !text-smoke_coal">
            {t(title)}
          </Typography>
        </Flex>
        {!!subtitle && (
          <Typography
            isTruncated
            className="!text-xs  !text-secondaryDisabledText"
          >
            {t(subtitle)}
          </Typography>
        )}
      </Flex>
      <IconButton
        type="far"
        name="chevron-right"
        size="md18"
        colorSchema="transparent"
        className={classes.arrowButton}
      />
    </CP>
  );
};

export default PreferencesSingleItem;
