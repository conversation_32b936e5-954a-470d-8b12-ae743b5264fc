@import '/src/shared/theme/theme.scss';

@layer organism {
  .headerItemWrapper {
    flex-direction: row;
    align-items: center;
    border-radius: 4px;
    background: colors(background);
    cursor: pointer;
  }
  .textWrapper {
    overflow: hidden;
    flex: 1;
    padding: 8px;
  }
  .titleWrapper {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: variables(gutter) * 0.5;
  }
  .subtitle {
  }
  .arrowButton {
    &:hover {
      background-color: unset;
    }
  }
  @media (min-width: breakpoints(tablet)) {
    .textWrapper {
      // padding: variables(largeGutter) 0;
    }
  }
}
