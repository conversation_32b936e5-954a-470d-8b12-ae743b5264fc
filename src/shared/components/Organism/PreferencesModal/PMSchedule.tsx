import React, { useCallback } from 'react';
import { useProfilePanelState } from 'shared/hooks/useSettingsState';
import Flex from 'shared/uikit/Flex';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ICloudCalendarIntegrationModal from './ICloudCalendarIntegration';
import classes from './PMSchedule.module.scss';
import ScheduleAvailabilityPreferencesModal from './PMScheduleAvailabilityList';
import ScheduleCalendarPreferencesModal from './PMScheduleCalendar';
import ScheduleMeetingPreferencesModal from './PMScheduleMeeting';
import PreferencesSingleItem from './PreferencesSingleItem';
import type { ComponentProps, FC } from 'react';
import cnj from '@shared/uikit/utils/cnj';

interface Props {
  onBack: () => void;
}

const subModalComponents = {
  availability: ScheduleAvailabilityPreferencesModal,
  calendar: ScheduleCalendarPreferencesModal,
  meeting: ScheduleMeetingPreferencesModal,
  icloudCalendarIntegration: ICloudCalendarIntegrationModal,
} as const;

const SchedulePreferencesModal: FC<Props> = ({ onBack }) => {
  const { state, previous, setProfilePanelState, closePanel } =
    useProfilePanelState();
  const opennedSubModal = Object?.keys?.(
    state?.settings?.preferences?.schedule || {}
  )?.[0] as keyof typeof subModalComponents;
  const { t } = useTranslation();

  const items: Array<ComponentProps<typeof PreferencesSingleItem>> = [
    { title: 'general', subtitle: 'default_time_formats' },
    {
      title: 'calendars',
      subtitle: 'integration_subtitle',
      onClick: () =>
        setProfilePanelState({
          settings: { preferences: { schedule: { calendar: true } } },
        }),
    },
    {
      title: 'meeting_tools',
      subtitle: 'integration_subtitle',
      onClick: () =>
        setProfilePanelState({
          settings: { preferences: { schedule: { meeting: true } } },
        }),
    },
    {
      title: 'availability',
      subtitle: 'set_availability',
      onClick: () =>
        setProfilePanelState({
          settings: { preferences: { schedule: { availability: true } } },
        }),
    },
    // { title: 'tasks', subtitle: '' },
    // { title: 'events', subtitle: 'birthdays_workshops' },
  ];

  const onCloseProfilePanel = () => {
    closePanel();
  };

  const onBackCallback = useCallback(
    () =>
      previous !== undefined
        ? setProfilePanelState(previous)
        : setProfilePanelState({
            settings: { preferences: { schedule: {} } },
          }),
    [setProfilePanelState, previous]
  );
  if (opennedSubModal) {
    const SubModal = subModalComponents[opennedSubModal];

    return <SubModal onBack={onBackCallback} />;
  }

  return (
    <FixedRightSideModalDialog onClickOutside={onCloseProfilePanel}>
      <Flex className={classes.root}>
        <ModalHeaderSimple
          noCloseButton
          hideBack={false}
          backButtonProps={{
            onClick: onBack,
          }}
          title={t('schedule_preferences')}
        />

        <Flex className={cnj(classes.body, 'gap-12')}>
          {items.map((item) => (
            <PreferencesSingleItem key={`pms-${item.title}`} {...item} />
          ))}
        </Flex>
      </Flex>
    </FixedRightSideModalDialog>
  );
};

export default SchedulePreferencesModal;
