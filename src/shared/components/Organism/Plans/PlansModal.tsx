import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { type MouseEvent } from 'react';
import Button from '@shared/uikit/Button';
import ModalHeaderBase from '@shared/uikit/Modal/BasicModal/Headers/Base/Base.component';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import useMedia from '@shared/uikit/utils/useMedia';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { type Plan } from '@shared/utils/normalizers/plansNormalizer';
import PlanCard from '../../molecules/PlanCad/PlanCard';
import TimeSpanSwitch from './partails/TimeSpanSwitch';
import classes from './PlansModal.module.scss';

interface PlansModalProps {
  plans?: Plan[];
  onSelect: (plan: Plan) => (event?: MouseEvent<any>) => void;
  onSeeMore: (plan?: Plan) => (event?: MouseEvent<any>) => void;
  handleOpenBillings: (event?: MouseEvent<any>) => void;
  onCancelSubscription: (
    planName?: string
  ) => ((event?: MouseEvent<any>) => void) | undefined;
  onClose?: (e?: any) => void;
  isCanceling: boolean;
}

const OPEN_PLANS = 'openPlans';

export default function PlansModal({
  plans,
  onSelect,
  onSeeMore,
  onClose,
  onCancelSubscription,
  handleOpenBillings,
  isCanceling,
}: PlansModalProps) {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();
  const searchParams = useSearchParams();
  const openPlans = searchParams.get(OPEN_PLANS);
  const isOpenPlans = openPlans === 'yes';
  const router = useRouter();
  const pathname = usePathname();

  const RenderRightContent = () => {
    if (isOpenPlans) return null;

    return (
      <Button
        label={t('billing')}
        schema="ghost-brand"
        className="ml-auto"
        onClick={handleOpenBillings}
      />
    );
  };

  const onRemoveParams = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete(OPEN_PLANS);

    const newUrl = params.toString()
      ? `${pathname}?${params.toString()}`
      : pathname;
    router.replace(newUrl);
  };
  const onCloseHandler = (e?: any) => {
    onClose?.(e);
    onRemoveParams();
  };

  return (
    <>
      <ModalHeaderBase noCloseButton>
        <TimeSpanSwitch
          items={[
            { label: 'monthly', value: 'MONTHLY' },
            {
              label: 'annually',
              value: 'ANNUALLY',
              promotionBadgeText: 'save_20',
            },
          ]}
        />
      </ModalHeaderBase>
      <ModalBody className={classes.plansListWrapper}>
        {plans?.map((item, idx) => (
          <PlanCard
            key={item?.id || idx}
            {...item}
            onSeeMore={onSeeMore(item)}
            onSelect={isTabletAndLess ? onSelect(item) : undefined}
            buttonProps={
              item?.isActive && Number(item?.price) > 0
                ? {
                    onClick: onCancelSubscription(item?.title),
                    label: t('cancel_subscription'),
                    schema: 'gray-semi-transparent',
                    isLoading: isCanceling,
                  }
                : undefined
            }
          />
        ))}
      </ModalBody>
      <ModalFooter>
        <Button
          label={t('compare_all_features')}
          schema="semi-transparent"
          rightIcon="chevron-right"
          rightColor="brand"
          rightSize={12}
          onClick={onSeeMore()}
        />
      </ModalFooter>
    </>
  );
}
