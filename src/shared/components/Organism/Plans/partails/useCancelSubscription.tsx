import React, { useCallback } from 'react';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Flex from '@shared/uikit/Flex';
import InfoCard from '@shared/uikit/InfoCard';
import useToast from '@shared/uikit/Toast/useToast';
import Typography from '@shared/uikit/Typography';
import { cancelPlan } from '@shared/utils/api/page';
import { getPortal } from '@shared/utils/getAppEnv';
import useBusinessPage from '@shared/utils/hooks/useBusinessPage';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';

const useCancelSubscription = () => {
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm({
    isNarrow: true,
    variant: 'normal',
  });
  const toast = useToast();
  const portalName = getPortal().toUpperCase();
  const { data: businessPage } = useBusinessPage({ isEnabled: true });
  const { mutate: cancelSubscription, isLoading } = useReactMutation({
    apiFunc: cancelPlan,
    onSuccess: () => {
      toast({
        type: 'success',
        icon: 'check-circle',
        message: t('subscription_canceled_succ'),
      });
      window.location.reload();
    },
  });

  const handleCancel =
    (payload: {
      planName: string;
      portalName: string;
      timeSpan: string;
      page: string;
    }) =>
    () => {
      openConfirmDialog({
        title: `${t('cancel_subscription')}?`,
        message: (
          <Flex className="gap-20">
            <Typography size={15}>{t('cancel_plan_message')}</Typography>
            <InfoCard
              hasLeftIcon
              leftIconProps={{
                name: 'exclamation-triangle',
                color: 'warning',
              }}
              classNames={{ wrapper: '!bg-warning_10 ' }}
              label={t('cancel_y_p_w_eff_im_a_c_undo')}
            />
          </Flex>
        ),
        confirmButtonText: t('confirm'),
        cancelButtonText: t('discard'),
        confirmCallback: () => {
          cancelSubscription(payload);
        },
      });
    };

  const onCancelSubscription = useCallback(
    ({ planName, timeSpan }) =>
      handleCancel({
        planName: planName.toUpperCase(),
        portalName,
        timeSpan,
        page: businessPage?.id,
      }),
    [businessPage?.id, portalName]
  );

  return { onCancelSubscription, isLoading };
};
export default useCancelSubscription;
