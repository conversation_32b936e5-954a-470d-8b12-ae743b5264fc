@import '/src/shared/theme/theme.scss';

.timeSpanBadge {
  height: fit-content;
  padding: variables(gutter) * 0.25;
  .badgeText {
    padding: variables(gutter) * 0.25;
  }
}
.promotionBadge {
  padding: variables(gutter) * 0.25 variables(gutter) * 0.25;
  height: auto;
}
.innerHeaderWrapper {
  flex-direction: row;
  gap: variables(gutter) * 0.5;
  justify-content: flex-start;
  align-items: center;
}
