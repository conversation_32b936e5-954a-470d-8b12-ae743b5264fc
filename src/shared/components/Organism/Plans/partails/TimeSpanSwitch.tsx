import React, { type MouseEvent, useCallback } from 'react';
import useGetPagePlanInfo from '@shared/hooks/api-hook/useGetPagePlanInfo';
import {
  selectData,
  setNineDotPanelState,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import { type PlanTimeSpan } from '@shared/types/page';
import Badge from '@shared/uikit/Badge';
import BaseButton from '@shared/uikit/Button/BaseButton';
import Flex, { type FlexProps } from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import classes from './partials.module.scss';
import type { BadgeProps } from '@mui/material';
import type { PlansModalData } from '@shared/components/Organism/Plans/types';

type TimeSpanSwitchItem = {
  label: string;
  value: PlanTimeSpan;
  promotionBadgeText?: string;
  promotionBadgeProps?: Partial<BadgeProps>;
};

interface TimeSpanSwitchProps extends FlexProps {
  items: TimeSpanSwitchItem[];
}
function TimeSpanSwitch({ items }: TimeSpanSwitchProps) {
  const { t } = useTranslation();
  const currentData = useNineDotPanelState<PlansModalData>(selectData);
  const { data: planInfo } = useGetPagePlanInfo();

  const setTimespan = (timeSpan: PlanTimeSpan) => (e?: MouseEvent<any>) => {
    setNineDotPanelState({ data: { ...currentData, timeSpan } });
  };

  const isActiveTimespan = useCallback(
    (value: PlanTimeSpan) =>
      value === (currentData?.timeSpan || planInfo?.activePlan?.timeSpan),
    [currentData?.timeSpan, planInfo?.activePlan?.timeSpan]
  );
  if (!items?.length) return null;

  return (
    <Flex className={classes.innerHeaderWrapper}>
      {items?.map(
        ({ label, value, promotionBadgeText, promotionBadgeProps }, idx) => (
          <BaseButton
            onClick={setTimespan(value)}
            key={`timespan-badge-${idx}`}
          >
            <Badge
              text={t(label)}
              textProps={{
                font: '700',
                size: 15,
                height: 16,
                color: isActiveTimespan(value)
                  ? 'darkSecondary_hover_50'
                  : 'primaryText',
                className: classes.badgeText,
              }}
              background={
                isActiveTimespan(value)
                  ? 'primaryText'
                  : 'darkSecondary_hover_50'
              }
              className={classes.timeSpanBadge}
              endComponent={
                promotionBadgeText ? (
                  <Badge
                    text={t(promotionBadgeText)}
                    textProps={{
                      font: '500',
                      size: 13,
                      height: 16,
                      color: isActiveTimespan(value)
                        ? 'secondaryText'
                        : 'brand',
                    }}
                    background={isActiveTimespan(value) ? 'brand' : 'brand_10'}
                    borderColor={isActiveTimespan(value) ? 'brand' : 'brand_10'}
                    className={classes.promotionBadge}
                  />
                ) : undefined
              }
            />
          </BaseButton>
        )
      )}
    </Flex>
  );
}

export default TimeSpanSwitch;
