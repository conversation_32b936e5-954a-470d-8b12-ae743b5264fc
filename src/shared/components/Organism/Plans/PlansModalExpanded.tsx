import React, { useMemo, type MouseEvent } from 'react';
import Badge from '@shared/uikit/Badge';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import ModalHeaderBase from '@shared/uikit/Modal/BasicModal/Headers/Base/Base.component';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { type Plan } from '@shared/utils/normalizers/plansNormalizer';
import PlanCardPrice from '../../molecules/PlanCad/PlanCard.price';
import TimeSpanSwitch from './partails/TimeSpanSwitch';
import classes from './PlansModalExpanded.module.scss';

interface PlansModalExpandedProps {
  plans?: Plan[];
  onSelect: (plan: Plan) => (event?: MouseEvent<any>) => void;
  onBack: (e: any) => void;
  handleOpenBillings: (event?: MouseEvent<any>) => void;
  onCancelSubscription: (
    planName?: string
  ) => ((event?: MouseEvent<any>) => void) | undefined;
  isBusy?: boolean;
  isCanceling: boolean;
}

export default function PlansModalExpanded({
  plans,
  onBack,
  onSelect,
  onCancelSubscription,
  handleOpenBillings,
  isBusy,
  isCanceling,
}: PlansModalExpandedProps) {
  const { t } = useTranslation();
  const featuresTableRows = useMemo<string[]>(() => {
    if (!plans) return [];

    return plans[plans.length - 1]?.features?.map(({ label }) => label);
  }, [plans]);

  return (
    <FixedRightSideModal
      modalDialogClassName={classes.wideModalDialog}
      modalClassName={classes.wideModalRoot}
      contentClassName={classes.contentClassName}
      isOpenAnimation
      onClickOutside={onBack}
      onBack={onBack}
      wide
    >
      <ModalHeaderSimple
        hideBack={false}
        noCloseButton
        title={t('compare_all_features')}
        rightContent={() => (
          <Button
            label={t('billing')}
            schema="ghost-brand"
            className="ml-auto"
            onClick={handleOpenBillings}
          />
        )}
      />
      <ModalHeaderBase noCloseButton>
        <TimeSpanSwitch
          items={[
            { label: 'monthly', value: 'MONTHLY' },
            {
              label: 'annually',
              value: 'ANNUALLY',
              promotionBadgeText: 'save_20',
            },
          ]}
        />
      </ModalHeaderBase>
      <ModalBody>
        <Flex className={classes.tableHeaderWrapper}>
          <Flex className={classes.tableColumn}>
            <Typography size={20} font="700" className="mt-auto">
              {t('all_features')}
            </Typography>
          </Flex>
          {plans?.map(
            (
              {
                Logo,
                label,
                color,
                price,
                priceUnit,
                id,
                isActive,
                title,
                ...rest
              },
              idx
            ) => (
              <Flex className={classes.tableColumn} key={`column-${idx}`}>
                <Flex className={classes.infoCard}>
                  <Logo />
                  <Typography color={color} size={16} height={20} font="bold">
                    {label}
                  </Typography>
                </Flex>
                <PlanCardPrice
                  price={price}
                  variant="Vertical"
                  priceUnit={priceUnit}
                  key={`price-${id}`}
                  wrapperClassName="mb-12"
                />
                <>
                  {isActive ? (
                    <Badge
                      background="success_10"
                      text={t('active')}
                      startIconProps={{
                        color: 'success',
                        name: 'check',
                        size: 16,
                      }}
                      textProps={{
                        color: 'success',
                        size: 16,
                      }}
                      className={classes.activeBadge}
                    />
                  ) : (
                    <Badge
                      text={Number(price) === 0 ? '-' : ' '}
                      background="transparent"
                      textProps={{
                        size: 16,
                        color: 'secondaryDisabledText',
                      }}
                      className={classes.activeBadge}
                    />
                  )}
                  {Number(price) === 0 ? (
                    <Badge
                      text={' '}
                      background="transparent"
                      textProps={{
                        size: 16,
                        color: 'secondaryDisabledText',
                      }}
                      className={classes.activeBadge}
                    />
                  ) : isActive ? (
                    <Button
                      label={t('cancel_subscription')}
                      schema="gray-semi-transparent"
                      className={classes.selectPlan}
                      onClick={onCancelSubscription(title)}
                      isLoading={isCanceling}
                    />
                  ) : (
                    <Button
                      label={t('select_plan')}
                      schema="primary-blue"
                      className={classes.selectPlan}
                      disabled={label !== 'Standard'}
                      onClick={onSelect({
                        Logo,
                        label,
                        color,
                        price,
                        priceUnit,
                        id,
                        isActive,
                        title,
                        ...rest,
                      })}
                      isLoading={isBusy && label === 'Standard'}
                    />
                  )}
                </>
              </Flex>
            )
          )}
        </Flex>
        <Flex className={classes.tableWrapper}>
          <Flex className={classes.tableColumn}>
            {featuresTableRows.map((label, idx) => (
              <Typography
                key={`feature-${idx}-label`}
                height={24}
                size={15}
                className={classes.rowHeaderContent}
              >
                {t(label)}
              </Typography>
            ))}
          </Flex>
          {plans?.map(({ features }, planIdx) => (
            <Flex
              key={`plan-${planIdx}-features`}
              className={classes.tableColumn}
            >
              {features?.map(({ Render }, idx) => (
                <Flex
                  key={`plan-${planIdx}-feature-${idx}`}
                  className={classes.cellContent}
                >
                  {Render}
                </Flex>
              ))}
            </Flex>
          ))}
        </Flex>
      </ModalBody>
    </FixedRightSideModal>
  );
}
