import { type JSX } from 'react';
import Carousel from '@shared/uikit/Carousel';
import Flex from '@shared/uikit/Flex';
import cnj from '@shared/uikit/utils/cnj';
import Tabs from 'shared/components/Organism/Tabs';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useManagerContext } from './CandidateManager.context';
import classes from './CandidateManager.module.scss';
import CandidateLinkedCount from './components/CandidateLinkedCount';
import { CandidateManagerDetailsView } from './components/DetailsView';
import FilterBody from './components/FiltersBody/FilterBody.compoent';
import { CandidateManagerJobControl } from './components/JobControl';
import { CandidateManagerPaginationHeader } from './components/PaginationHeader';
import { UpcomingMeetingsNotice } from './components/UpcomingMeetingsNotice';
import CandidateManagerPanels from './tabs';
import CreateCandidateFromUser from './tabs/CreateCandidateFromUser';
import useCandidateManagerNotesFilters from './tabs/tab1.notes/useCandidateManagerNotesFilters';
import useCandidateManagerTodosFilters from './tabs/tab2.todos/useCandidateManagerTodosFilters';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';

const tabs: Array<{ path: CandidateManagerTabkeys; title: string }> = [
  {
    path: 'notes',
    title: 'notes',
  },
  {
    path: 'todos',
    title: 'todos',
  },
  {
    path: 'meetings',
    title: 'meetings',
  },
  {
    path: 'documents',
    title: 'documents',
  },
  {
    path: 'reviews',
    title: 'reviews',
  },
  {
    path: 'skillboard',
    title: 'Skill Board',
  },
  {
    path: 'threads',
    title: 'threads',
  },
  {
    path: 'emails',
    title: 'emails',
  },
  {
    path: 'assessments',
    title: 'assessments',
  },
];

const CandidateManagerLayout = (): JSX.Element => {
  const { t } = useTranslation();
  const { isCandidate, isLoading, activeFilter, selectedTab, setSelectedTab } =
    useManagerContext();

  const notesFilterGroups = useCandidateManagerNotesFilters();
  const todosFilterGroups = useCandidateManagerTodosFilters();

  return (
    <Flex className="gap-20 overflow-hidden flex-1">
      <UpcomingMeetingsNotice />
      <CandidateManagerPaginationHeader />
      <CandidateLinkedCount />
      <Flex flexDir="row" className="gap-20 flex-1 overflow-hidden">
        <Flex className={cnj(classes.scrollFix, 'flex-1 relative')}>
          <CandidateManagerDetailsView className="flex-1" />
          <Flex className="sticky z-10 bottom-0 w-full !bg-tooltipText">
            <CandidateManagerJobControl />
          </Flex>
        </Flex>
        {!isLoading && !isCandidate ? (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <CreateCandidateFromUser />
          </Flex>
        ) : activeFilter === selectedTab && activeFilter === 'notes' ? (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <FilterBody rootName="notes" groups={notesFilterGroups} />
          </Flex>
        ) : activeFilter === selectedTab && activeFilter === 'todos' ? (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <FilterBody rootName="todos" groups={todosFilterGroups} />
          </Flex>
        ) : (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <Carousel>
              <Tabs
                autoScrollActiveTab
                className="!pt-4"
                activePath={selectedTab}
                onChangeTab={setSelectedTab}
                tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
                divide
              />
            </Carousel>
            <CandidateManagerPanels active={selectedTab} />
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default CandidateManagerLayout;
