/* eslint-disable class-methods-use-this */
import BT, {
  type Client,
  type DataCollector,
  type BraintreeError,
  type VaultManager,
} from 'braintree-web';
import Cookies from "@shared/utils/toolkit/cookies";
import getCookieKey from "@shared/utils/toolkit/getCookieKey";
import {getPaymentClientToken} from "@shared/utils/api/payment";

const isBraintreeError = (error: unknown): error is BraintreeError =>
  !!(error as BraintreeError)?.type;

type SavedCard = {
  id: string;
  last4digits: string;
  cardType: string | null;
};
export type BEPaypentTokenResponse = {
  token: string;
  methods: SavedCard[];
};

export class BraintreeClient {
  private static clientToken: string | null = null;
  private static ClientInstance: Client | null;
  private static DataCollectorInstance: DataCollector | null;
  private static VaultManagerInstance: VaultManager | null;
  private static SavedCards: SavedCard[] = [];
  private static retryCount = 0;

  private static COOKIE_KEY = getCookieKey('braintreeClientToken');

  private static async getToken() {
    if (this.retryCount > 2)
      throw new Error('Error initializing payment, please contact support');
    try {
      if (BraintreeClient.clientToken) return BraintreeClient.clientToken;
      try {
        const cookieJSON = Cookies.get(BraintreeClient.COOKIE_KEY);
        if (cookieJSON) {
          const { token, methods } = JSON.parse(
            cookieJSON
          ) as BEPaypentTokenResponse;

          if (methods) BraintreeClient.SavedCards = methods;
          if (token) {
            BraintreeClient.clientToken = token;

            return token;
          }
        }
      } catch {
        /* empty */
      }

      const response = await getPaymentClientToken();
      BraintreeClient.clientToken = response.token;
      BraintreeClient.SavedCards = response.methods;

      Cookies.set(BraintreeClient.COOKIE_KEY, JSON.stringify(response), {
        expires: undefined,
      });
      BraintreeClient.retryCount = 0;

      return response.token as string;
    } catch (error) {
      throw new Error(
        `Error Fetching client Token: ${(error as { message?: string })?.message}`
      );
    }
  }

  private static async getClientInstance() {
    if (BraintreeClient.ClientInstance?.authorization)
      return BraintreeClient.ClientInstance;

    const clientToken = await BraintreeClient.getToken();
    if (!clientToken)
      throw new Error(
        'failed to initialize client instance, could not find an auth token.'
      );
    try {
      const client = await BT.client.create({
        authorization: `${clientToken}`,
      });
      BraintreeClient.ClientInstance = client;

      return client;
    } catch (error) {
      BraintreeClient.handleBTError(error, true);

      return null;
    }
  }

  private static async getDataCollector() {
    if (BraintreeClient.DataCollectorInstance)
      return BraintreeClient.DataCollectorInstance;
    const client = await BraintreeClient.getClientInstance();
    if (!client) {
      // return;
      throw new Error('could not initialize client');
    }
    const dataCollector = await BT.dataCollector.create({ client });
    BraintreeClient.DataCollectorInstance = dataCollector;

    return dataCollector;
  }

  private static async getVaultInstance() {
    if (BraintreeClient.VaultManagerInstance)
      return BraintreeClient.VaultManagerInstance;
    const authorization = await BraintreeClient.getToken();
    const client = await BraintreeClient.getClientInstance();
    if (!client) throw new Error('could not initialize client');
    const vault = await BT.vaultManager.create({ client, authorization });
    BraintreeClient.VaultManagerInstance = vault;

    return vault;
  }

  static async getSavedCards() {
    return BraintreeClient.SavedCards?.map(({ last4digits, id }) => ({
      label: `**** - **** - **** - ${last4digits}`,
      value: id,
    }));
  }

  private static async handleBTError(error: unknown, reInit?: boolean) {
    // logger required to monitor Braintree errors
    // console.warn(error);
    const localInstance = new BraintreeClient();
    if (isBraintreeError(error)) {
      if (error.code?.includes('AUTHORIZATION')) {
        await localInstance.teardown();
        Cookies.remove('bci');
        BraintreeClient.clientToken = null;
      }
    }
    if (reInit) {
      BraintreeClient.retryCount += 1;
      localInstance.init();
    }
  }

  async init() {
    try {
      const clientToken = await BraintreeClient.getToken();
      const client = await BraintreeClient.getClientInstance();
      const dataCollector = await BraintreeClient.getDataCollector();
      const vault = await BraintreeClient.getVaultInstance();
      const paymentMethods = await BraintreeClient.getSavedCards();

      return { clientToken, client, dataCollector, vault, paymentMethods };
    } catch (error) {
      console.log(error);
      BraintreeClient.handleBTError(error, true);

      return {};
    }
  }

  async teardown() {
    if (BraintreeClient.DataCollectorInstance) {
      await BraintreeClient.DataCollectorInstance.teardown();
      BraintreeClient.DataCollectorInstance = null;
    }
    if (BraintreeClient.ClientInstance)
      await BraintreeClient.ClientInstance.teardown(() => {
        BraintreeClient.ClientInstance = null;
      });
  }

  getClientToken() {
    return BraintreeClient.getToken();
  }
  getClient() {
    return BraintreeClient.getClientInstance();
  }
}
