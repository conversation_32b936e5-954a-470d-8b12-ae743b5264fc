import { useEffect, useState } from 'react';
import { BraintreeClient } from './BraintreeClient';
import type {
  BraintreeError,
  Client,
  DataCollector,
  FetchPaymentMethodsPayload,
} from 'braintree-web';

const normalizedPaymentMethods = (
  data: FetchPaymentMethodsPayload
): { label: string; value: string } => ({
  label: !data?.details
    ? ''
    : 'lastFour' in data?.details
      ? `**** - **** - **** - ${data.details.lastFour}`
      : 'lastTwo' in data.details
        ? `**** - **** - **** - **${data.details.lastTwo}`
        : '',
  value: data?.nonce,
});

export default function useBraintree(
  callback?: (client: Client, dataCollector: DataCollector) => void
) {
  const [client, setClient] = useState<Client | null>(null);
  const [dataCollector, setDataCollector] = useState<DataCollector | null>(
    null
  );
  const [paymentMethods, setPaymentMethods] = useState<
    { label: string; value: string }[]
  >([]);

  const [isInitialized, setInitialized] = useState(false);

  const handleInitializeBT = async (
    callback?: (client: Client, dataCollector: DataCollector) => void
  ) => {
    const BTClient = new BraintreeClient();
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const { client, dataCollector, paymentMethods } = await BTClient.init();
    if (!client) return;
    if (paymentMethods) setPaymentMethods(paymentMethods);

    callback?.(client, dataCollector);
    setTimeout(() => {
      setClient(client);
      setInitialized(true);
    }, 0);
  };

  const tokenize = (data: any) =>
    new Promise<{
      error?: BraintreeError;
      data?: { creditCards: { nonce: string }[] };
    }>((resolve) => {
      client?.request(
        {
          endpoint: 'payment_methods/credit_cards',
          method: 'post',
          data,
        },
        (error, data, ...rest) => resolve({ error, data, ...rest })
      );
    });

  useEffect(() => {
    handleInitializeBT(callback);
  }, [callback]);

  return { client, dataCollector, isInitialized, paymentMethods, tokenize };
}
