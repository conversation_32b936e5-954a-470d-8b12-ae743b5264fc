import React from 'react';
import CheckBox from '@shared/uikit/CheckBox';
import Flex from '@shared/uikit/Flex';
import TextLink from '@shared/uikit/Link/TextLink';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import { type FixedRightSideModalDialogProps } from '@shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import Typography from '@shared/uikit/Typography';
import { landingRouteNames } from '@shared/utils/constants';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import Button from 'shared/uikit/Button';
import Form from 'shared/uikit/Form';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import useTranslation from 'shared/utils/hooks/useTranslation';
import InvoiceSummary from './CheckoutModal.invoiceSummary';
import classes from './index.module.scss';
import useCheckoutForm from './useCheckoutModal';
import { type CheckoutChildModalData } from '.';

export interface CheckoutFormProps extends CheckoutChildModalData {
  isOpen: boolean;
  portal?: string;
  modalProps?: Partial<FixedRightSideModalDialogProps>;
}

export default function CheckoutForm({
  modalProps: parentModalProps = {},
  invoice,
  entityData,
  actions,
}: CheckoutFormProps) {
  const {
    initialData,
    groups,
    transform,
    validationSchema,
    apiFunc,
    onSuccess,
    onFailure,
    onCancel,
    modalProps,
  } = useCheckoutForm({
    entityData,
    invoice,
    actions,
    modalProps: parentModalProps,
  });
  const { t } = useTranslation();

  return (
    <Form
      initialValues={initialData}
      onSuccess={onSuccess}
      apiFunc={apiFunc}
      onFailure={onFailure}
      transform={transform}
      validationSchema={validationSchema}
      className={classes.form}
    >
      {(props) => (
        <FixedRightSideModal
          showConfirm
          onClickOutside={onCancel(props.dirty)}
          {...modalProps}
          wide
        >
          <div style={{ display: 'contents' }}>
            <ModalHeaderSimple
              title={t('checkout')}
              hideBack={false}
              noCloseButton
              backButtonProps={{ onClick: onCancel(props.dirty) }}
            />
            <ModalBody className={classes.fullHeight}>
              <DynamicFormBuilder groups={groups(props)} />
            </ModalBody>
            <ModalFooter>
              <InvoiceSummary
                formikProps={props}
                {...invoice}
                discount={props.values.discount}
                isLoading={false}
              />
              <Flex className="mb-20">
                <CheckBox
                  classNames={{
                    checkbox: '!mr-8',
                  }}
                  label={
                    <ParseTextStringCP
                      textProps={{
                        height: 19,
                        size: 15,
                        color: 'primaryText',
                      }}
                      textString={translateReplacer(
                        t('im_agree_with_name_term'),
                        [t('subscription_agreement')]
                      )}
                      tagComponentMap={{
                        0: (text) => (
                          <TextLink
                            typographyProps={{
                              size: 14,
                              color: 'brand',
                              className: 'underline decoration-brand',
                            }}
                            href={landingRouteNames.userAgreement}
                            linkProps={{ target: '_blank' }}
                          >
                            {text}
                          </TextLink>
                        ),
                      }}
                    />
                  }
                  value={props.values.subscriptionAgreement}
                  onChange={(val) =>
                    props.setFieldValue('subscriptionAgreement', val)
                  }
                />
                {props.errors?.subscriptionAgreement &&
                  props.submitCount > 0 && (
                    <Typography mt={4} color="error" size={13} height={15}>
                      {t(props.errors?.subscriptionAgreement)}
                    </Typography>
                  )}
              </Flex>
              <Flex className={classes.footer}>
                <Button
                  fullWidth
                  label={t('discard')}
                  schema="gray-semi-transparent"
                  onClick={onCancel(props.dirty)}
                />
                <SubmitButton
                  fullWidth
                  schema="primary-blue"
                  labelFont="bold"
                  label={t('complete_checkout')}
                />
              </Flex>
            </ModalFooter>
          </div>
        </FixedRightSideModal>
      )}
    </Form>
  );
}
