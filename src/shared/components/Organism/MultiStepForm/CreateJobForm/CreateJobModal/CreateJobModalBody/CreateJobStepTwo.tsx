import { addDays, startOfDay } from 'date-fns';
import { useFormikContext } from 'formik';
import { motion } from 'framer-motion';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import geoApi from 'shared/utils/api/geo';
import { Endpoints, jobsDb } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './CreateJobModalBody.module.scss';
import type { CreateJobFormDataProps } from 'shared/types/jobsProps';

const CreateJobStepTwo = () => {
  const { t } = useTranslation();
  const { values } = useFormikContext<CreateJobFormDataProps>();
  const normalizer = (response: any) =>
    response &&
    response
      .map(({ id: value, title }: any) => ({
        label: title,
        name: value,
        value,
      }))
      ?.slice(0, 10);

  return (
    <motion.div
      className={classes.wrapper}
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <DynamicFormBuilder
        className={classes.root}
        groups={[
          {
            formGroup: {
              title: t('general_info'),
              formSection: true,
            },
            name: 'title',
            wrapStyle: classes.item,
            label: t('title'),
            required: true,
            cp: 'asyncAutoComplete',
            url: Endpoints.App.Common.getOccupations,
            visibleRightIcon: true,
            rightIconProps: { name: 'search' },
            normalizer,
          },
          {
            name: 'category',
            label: t('category'),
            required: true,
            cp: 'asyncAutoComplete',
            type: 'text',
            visibleRightIcon: true,
            rightIconProps: { name: 'search' },
            url: Endpoints.App.Common.getIndustry,
            rowContainerClassName: classes.rowContainerClassName,
            normalizer,
          },
          {
            name: 'workPlaceType',
            cp: 'dropdownSelect',
            label: t('workspace_type'),
            wrapStyle: classes.formItem,
            options: jobsDb.WORK_SPACE_MODEL,
            halfWidthWhenOpen: true,
            isFirstHalfWidth: true,
            required: true,
            styles: {
              options: `!w-right-side-big`,
            },
          },
          {
            name: 'employmentType',
            cp: 'dropdownSelect',
            label: t('job_type'),
            wrapStyle: classes.formItem,
            options: jobsDb.JOB_EMPLOYMENT_TYPE_MODEL,
            halfWidthWhenOpen: true,
            isSecondHalfWidth: true,
            required: true,
            styles: {
              options: `!w-right-side-big !right-20 !left-[unset]`,
            },
          },
          {
            name: 'responseTime',
            cp: 'dropdownSelect',
            label: t('response_time'),
            wrapStyle: classes.formItem,
            options: jobsDb.JOB_RESPONSE_TIME_MODEL,
            halfWidthWhenOpen: true,
            isFirstHalfWidth: true,
            styles: {
              options: `!w-right-side-big`,
            },
          },
          {
            name: 'numberOfHires',
            cp: 'numberInput',
            label: t('number_of_hires'),
            wrapStyle: classes.formItem,
            forceVisibleError: true,
            halfWidthWhenOpen: true,
            isSecondHalfWidth: true,
            maxLength: 3,
          },
          {
            name: 'schedulePublishDateTime',
            cp: 'datePicker',
            wrapStyle: classes.formItem,
            variant: 'input',
            minDate: addDays(startOfDay(new Date()), 1),
            picker: 'day',
            label: t('schedule_publish'),
            forceVisibleError: true,
          },
          {
            apiFunc: geoApi.suggestCity,
            cp: 'asyncAutoCompleteWithExtraParams',
            label: t('location'),
            name: 'locationWithExtraParams',
            required: true,
          },
          {
            cp: 'checkBox',
            name: 'globalApplyAllowed',
            label: t('allow_apply_globally'),
            disabled: values?.workPlaceType?.value === 'ON_SITE',
          },
          {
            formGroup: {
              title: t('external_job'),
              formSection: true,
            },
            name: 'websiteUrl',
            cp: 'addExternalJobLink',
            placeholder: t('website_link'),
            wrapStyle: 'mt-8',
          },
        ]}
      />
    </motion.div>
  );
};

export default CreateJobStepTwo;
