import { useEffect, useMemo, type FC } from 'react';
import Observer from '@shared/components/atoms/Observer';
import ValidationFormButton from '@shared/components/atoms/ValidationFormButton';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import isEmptyObjectValues from '@shared/utils/toolkit/isEmptyObjectValues';
import removeRichTextBreakLines from '@shared/utils/toolkit/removeRichTextBreakLines';
import eventKeys from 'shared/constants/event-keys';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import { setStatus } from '@shared/utils/api/jobs';
import useResponseToast from '@shared/hooks/useResponseToast';
import { useQueryClient } from '@tanstack/react-query';
import { useCreateJobModal } from '../CreateJobModalProvider';
import classes from './CreateJobModalStyles.module.scss';
import useCreateJobSubForm, { handlePipeLines } from './useCreateJobSubForm';
import useCreateOrEditJob from './useCreateOrEditJob';
import type { MultiStepFormFooterProps } from '@shared/types/formTypes';
import type { PipelineProps } from '@shared/types/pipelineTypes';
import type { UserType } from '@shared/types/user';
import type {
  CreateJobAPIDataProps,
  CreateJobFormDataProps,
  JobPipelineTemplateProps,
  WorkAuthorizationProps,
  CreateJobOptions,
} from 'shared/types/jobsProps';
// eslint-disable-next-line import/no-cycle

const CreateJobModalFooter: FC<MultiStepFormFooterProps> = (props) => {
  const { isSubmitStep, setStep, isValid, status, step, validateForm, values } =
    props;
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { subForm, job } = useCreateJobModal();
  const { onClickDiscard } = useCreateJobSubForm();
  const { authUser } = useGetAppObject();
  const { onCreateOrEdit, loading } = useCreateOrEditJob({ step, setStep });
  const { options } = useMultiStepFormState('createJobForm');
  const onClose = () => closeMultiStepForm('createJobForm');

  useEffect(() => {
    if (options?.step) {
      setStep(options.step);
    }
  }, [options, setStep]);
  const submitText = useMemo(() => {
    if (!subForm || subForm === 'questions') return 'next';

    return 'save';
  }, [subForm]);
  const discardText = useMemo(() => {
    if (subForm === 'questions') return 'cancel';

    return 'discard';
  }, [subForm]);
  const handleClickDiscard = () => {
    if (subForm) return onClickDiscard();
    event.trigger(eventKeys.closeModal);

    return null;
  };
  const handleClickSave = () => {
    const jobData = handleCreateJobData(values, authUser);
    onCreateOrEdit(jobData);
  };

  const isJobPaid = job?.paid;

  return (
    <Flex className={classes.root}>
      <Observer step={step} validateForm={validateForm} />
      <Button
        fullWidth
        label={t(discardText)}
        schema="gray-semi-transparent"
        onClick={handleClickDiscard}
      />
      {isSubmitStep ? (
        isJobPaid ? (
          <Button
            fullWidth
            label={t('done')}
            schema="primary-blue"
            onClick={onClose}
          />
        ) : (
          <SubmitButton
            fullWidth
            label={t('checkout_publish')}
            schema="primary-blue"
          />
        )
      ) : (
        <ValidationFormButton
          label={t(submitText)}
          schema="primary-blue"
          disabled={loading || !isEmptyObjectValues(status)}
          onValidate={handleClickSave}
        />
      )}
    </Flex>
  );
};

export default CreateJobModalFooter;

const handleCreateJobData = (
  values: CreateJobFormDataProps,
  authUser?: UserType
) => {
  let collaborators: any[] = [
    {
      userId: authUser?.id ?? '',
      primary: !values.primary_collab?.userId,
    },
  ];
  if (values.collaboratorUsers?.length) {
    const collabs = values.collaboratorUsers.map((user) => ({
      userId: user.userId,
      primary: values.primary_collab?.userId
        ? user.userId === values.primary_collab?.userId
        : false,
    }));
    collaborators = values.id ? collabs : [...collaborators, ...collabs];
  }
  const pipelinesArray = handlePipeLines(values);
  const pipelines = pipelinesArray.reduce((acc, curr) => {
    let pipeline = curr;
    const autoReply = (values as any)[
      `auto_reply_${curr.title}`
    ] as JobPipelineTemplateProps;
    if (autoReply) {
      pipeline = {
        ...pipeline,
        ...autoReply,
        followupMessagePeriod: autoReply.followupMessagePeriod?.value,
      };
    }

    return [...acc, pipeline];
  }, [] as PipelineProps[]);
  const workAuthorizations = values.workAuthorizations?.reduce((acc, curr) => {
    if (!curr?.country?.value || !curr?.authorization?.value) return acc;

    return [
      ...acc,
      {
        id: curr.authorization.value,
        title: curr.authorization.label,
        countryCode: curr.country.value,
        country: curr.country.label,
      },
    ] as WorkAuthorizationProps[];
  }, [] as WorkAuthorizationProps[]);
  let jobData: CreateJobAPIDataProps = {
    projectIds: values.projects.map((project) => Number(project.id)),
    categoryId: values.category?.value,
    categoryName: values.category?.label,
    workPlaceType: values.workPlaceType?.value,
    employmentType: values.employmentType?.value,
    responseTime: values.responseTime?.value,
    skills: values.skills.map((skill) => ({
      skillId: skill.id,
      skillLevel: skill.skillLevel,
      skillName: skill.label,
    })),
    collaborators,
    priority: values.priority?.value,
    workDays: values.workDays?.map((day) => day.value),
    title: values.title?.label,
    titleId: values.title?.value,
    numberOfHires: values.numberOfHires,
    schedulePublishDateTime: values.schedulePublishDateTime,
    // scheduleCloseDateTime: values.scheduleCloseDateTime,
    location: values?.locationWithExtraParams,
    globalApplyAllowed: values.globalApplyAllowed,
    external: !!values.websiteUrl,
    websiteUrl: values.websiteUrl,
    description: removeRichTextBreakLines(values.description ?? ''),
    languages: values.languages?.map((lang) => ({
      languageId: Number(lang.id),
      languageLevel: lang.languageLevel,
      languageName: lang.label,
    })),
    hashtags: values.hashtags,
    tags: values.tags,
    // attachments: values.attachments?.map(file => file.)
    fileIds: values.attachments?.map((file) => file.id),
    emailRequired: values.emailRequired,
    resumeRequired: values.resumeRequired,
    phoneRequired: values.phoneRequired,
    coverLetterRequired: values.coverLetterRequired,
    experienceLevel: values.experienceLevel?.value,
    educationDegree: values.educationDegree?.value,
    contractDuration: values.contractDuration?.value,
    travelRequirement: values?.travelRequirement?.value,
    hoursPerDay: values.hoursPerDay?.value,
    hoursPerWeek: values.hoursPerWeek?.value,
    timezoneId: values.timezone?.value,
    timezoneCode: values.timezone?.code,
    timezoneLabel: values.timezone?.label,
    timezoneOffset: values.timezone?.offset,
    workAuthorizations,
    salaryCurrencyId: values.currency?.value,
    salaryCurrencyName: values.currency?.name,
    salaryCurrencySymbol: values.currency?.symbol,
    salaryCurrencyCode: values.currency?.code,
    salaryPeriod: values.salaryPeriod?.value,
    salaryRangeMin: Number(values.min_salary),
    salaryRangeMax: Number(values.max_salary),
    taxTerm: values.taxTermData?.value,
    taxTermId: values.taxTermData?.value,
    taxTermName: values.taxTermData?.label,
    markup: Number(values.markup?.value),
    benefits: values.benefits?.map((benefit) => ({
      id: benefit.value,
      title: benefit.label,
    })),
    pipelines,
    questions: [],
  };

  if (values.questionsData) {
    jobData = {
      ...jobData,
      questions: values.questionsData.questions.map((question) => ({
        ...question,
        type: question.type?.value,
        viewComponentType: question.viewComponentType?.value,
      })),
      totalScore: Number(values.questionsData.totalScore ?? 0),
      maxWrongQuestionCount: Number(
        values.questionsData.maxWrongQuestionCount ?? 0
      ),
    };
  }

  return jobData;
};
