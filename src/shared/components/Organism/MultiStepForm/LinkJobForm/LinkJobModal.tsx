import { useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import React, { useMemo } from 'react';
import useResponseToast from '@shared/hooks/useResponseToast';
import { linkJobsToCandidate } from '@shared/utils/api/candidates';
import {
  addCandidate,
  batchAddCandidatesToJobs,
  getCandidacyLinkedJobs,
} from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import { addJobsToProject } from 'shared/utils/api/project';
import formValidator from 'shared/utils/form/formValidator';
import getStepData from 'shared/utils/getStepData';
import useTranslation from 'shared/utils/hooks/useTranslation';
import MultiStepForm from '../MultiStepForm';
import classes from './LinkJobModal.module.scss';
import useLinkJobForm from './useLinkJobForm';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { IJobApi } from '@shared/types/job';
import type { FC } from 'react';

interface LinkJobModalProps {
  data?: {
    id: string;
    target: 'project' | 'candidate' | 'job';
    initialJobs: Array<{ id: string }>;
  };
}

const LinkJobModal: FC<LinkJobModalProps> = ({ data }) => {
  const queryClient = useQueryClient();
  const target = data?.target ?? '';
  const targetId = data?.id ?? '';
  const { t } = useTranslation();
  const params = useParams();
  const id = params?.id;
  const { options } = useMultiStepFormState('linkJobForm');
  const onClose = () => closeMultiStepForm('linkJobForm');
  const formData = useLinkJobForm(data?.target ?? 'project') as any[];
  const totalSteps = useMemo(() => formData.length ?? 0, [formData]);
  const { handleSuccess, handleError } = useResponseToast();
  const getHeaderProps = getStepData('getHeaderProps', formData);
  const getStepHeaderProps = getStepData('getStepHeaderProps', formData);
  const renderFooter = getStepData('renderFooter', formData);
  const renderBody = getStepData('renderBody', formData);
  const { handleChangeParams } = useCustomParams();

  const onAlert = (message: string, type: 'success' | 'error') => {
    if (type === 'success') {
      handleSuccess({
        message,
        title: t(target === 'project' ? 'job_linked' : 'candidate_submitted'),
      });
    } else {
      handleError({ message, title: '' });
    }
    onClose();
  };

  const linkedJobsQuery = useReactQuery<IJobApi[]>({
    action: {
      apiFunc: () => getCandidacyLinkedJobs(targetId || ''),

      key: [QueryKeys.getCandidateRecruiterJobsList, targetId],
    },
    config: {
      enabled: target === 'candidate' && Boolean(targetId),
      refetchOnWindowFocus: false,
    },
  });

  const { mutate: addJobsToProjectMutate } = useReactMutation({
    apiFunc: addJobsToProject,
    onSuccess: () => {
      setTimeout(() => {
        onAlert(t('job_linked_successfully'), 'success');
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.projectJobsList, targetId],
        });
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.projectDetails, targetId],
        });
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getPipelineParticipants],
          exact: false,
        });
      }, 500);
    },
  });

  const onSuccessAddCandidate = () => {
    onAlert(t('candidates_linked_successfully'), 'success');

    Promise.all([
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.jobCandidates, Number(targetId), '0'],
      }),
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.jobDetails, String(targetId)],
      }),
    ]);
    setTimeout(() => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getPipelinesList],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getPipeline, id],
      });
    }, 500);
    handleChangeParams({
      add: { refresh: 'true' },
    });
    options?.handleRefetch?.();
  };

  const { mutate: linkJobsToCandidateMutate } = useReactMutation({
    apiFunc: linkJobsToCandidate,
    onSuccess: () => {
      onAlert(t('your_candidate_submitted_success'), 'success');

      setTimeout(() => {
        Promise.all([
          queryClient.invalidateQueries({
            queryKey: [QueryKeys.getPipelineParticipants],
            exact: false,
          }),
          queryClient.invalidateQueries({
            queryKey: [QueryKeys.getCandidateActivities, targetId],
            exact: true,
          }),
          queryClient.invalidateQueries({
            queryKey: [QueryKeys.getCandidateRecruiterJobsList, targetId],
            exact: true,
          }),
        ]);
      }, 500);
    },
  });

  const { mutate: addCandidateMutate } = useReactMutation({
    apiFunc: addCandidate,
    onSuccess: onSuccessAddCandidate,
  });

  const { mutate: batchAddCandidatesToJobsMutate } = useReactMutation({
    apiFunc: batchAddCandidatesToJobs,
    onSuccess: onSuccessAddCandidate,
  });

  const initialValues = useMemo(
    () => ({
      jobs: data?.initialJobs ?? linkedJobsQuery.data ?? [],
    }),
    [data?.initialJobs, linkedJobsQuery.data]
  );

  const apiFunc: MultiStepFormProps['apiFunc'] = async ({
    jobs,
  }: {
    jobs: any[];
  }) => {
    const jobIds = jobs?.map((job) => job.id);
    if (target === 'project') {
      addJobsToProjectMutate({ projectId: targetId, jobIds });
    } else if (target === 'candidate') {
      linkJobsToCandidateMutate({ candidateId: targetId, jobIds });
    } else if (typeof targetId === 'string') {
      addCandidateMutate({ jobId: targetId, candidateIds: jobIds });
    } else {
      batchAddCandidatesToJobsMutate({
        jobIds: targetId,
        candidateIds: jobIds,
      });
    }
  };

  const getValidationSchema: MultiStepFormProps['getValidationSchema'] =
    React.useCallback(({ step }: { step: number }) => {
      const validationSchema = {
        1: {
          jobs: formValidator.array().min(1),
        },
      } as any;

      return formValidator.object().shape(validationSchema[step]);
    }, []);

  return (
    <MultiStepForm
      apiFunc={apiFunc}
      getValidationSchema={getValidationSchema}
      totalSteps={totalSteps}
      initialValues={initialValues}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      onFailure={(error) =>
        onAlert(error.message ?? error.defaultMessage ?? 'Error!', 'error')
      }
      enableReinitialize
      formName="createProjectForm"
      isOpenAnimation
      backdropClassName={classes.backdrop}
      bodyProps={{
        className: '!p-0',
      }}
    />
  );
};

export default LinkJobModal;
