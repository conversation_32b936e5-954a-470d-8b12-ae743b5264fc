import partial from 'lodash/partial';
import unionBy from 'lodash/unionBy';
import React, {
  useRef,
  useState,
  useEffect,
  useCallback,
  useId,
  useMemo,
  type MouseEvent,
} from 'react';
import { createPortal } from 'react-dom';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import addClassToNode from 'shared/utils/addClassToNode';
import useOutsideListener from 'shared/utils/hooks/useOutsideListener';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { elementIsVisibleInViewport } from 'shared/utils/toolkit/elementIsVisibleInViewport';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import BottomSheet from '../BottomSheet';
import Button from '../Button';
import IconButton from '../Button/IconButton';
import DividerDot from '../Divider/Divider.dot';
import Flex from '../Flex';
import Ripple from '../Ripple';
import Tag from '../Tag';
import TextInput from '../TextInput';
import Typography from '../Typography';
import OverflowTip from '../Typography/OverflowTip';
import cnj from '../utils/cnj';
import useMedia from '../utils/useMedia';
import useTheme from '../utils/useTheme';
import classes from './AutoComplete.component.module.scss';
import type { AutoCompleteProps } from '../types';
import type { AutoCompleteOption } from 'shared/types/components/Form.type';

const AutoComplete = <
  T extends AutoCompleteOption = AutoCompleteOption,
  M extends boolean = false,
>(
  props: AutoCompleteProps<T, M>
) => {
  const {
    label,
    helperText,
    value,
    options = [],
    onChangeInput,
    onSelect,
    onBlur: parentOnBlur,
    onFocus,
    error,
    isFocus,
    styles,
    style,
    disabled,
    rightIcon,
    leftIcon,
    displayName,
    trim,
    editable,
    placeholder,
    inputWrapClassName,
    withRightIconClassName,
    className,
    inputStyle,
    renderItem,
    renderContent,
    optionItemClassName,
    visibleCheck = true,
    isMulti,
    limit = 3,
    visibleRightIcon,
    rightIconClassName,
    rightIconProps = {},
    optionItemProps = {},
    maxLength,
    autoComplete,
    popperClassName,
    variant = 'form-input',
    visibleCharCounter = false,
    cp,
    doNotUseTranslation,
    disabledReadOnly,
    checkIsValid,
    textInputProps = {},
    optionsVariant = 'dropdown',
    onClick: onClickProp,
    isShow = false,
    onlyChooseFromOptions,
    parentOptionsVariant,
    handleConfirm,
    isInModal,
    ref: inputRef,
    cleanInputAfterSelect,
    showPreview,
    hasPriorityData,
    returnEmpty = true,
    optionKey,
    showEllipsis,
    onClear,
    clearable,
    leftIconClassName,
  } = props;
  const [key, setKey] = useState(new Date().toISOString());
  const [suggestedCompletion, setSuggestedCompletion] = useState<string>();
  const { t: translate } = useTranslation();
  const { theme } = useTheme();
  const { isDark } = theme;
  const [show, setShow] = useState(false);
  const nonArrayValue = !Array.isArray(value) ? value : undefined;
  const defaultDisplayName =
    displayName ||
    (nonArrayValue
      ? options.find((item) => item.value === nonArrayValue?.value)?.label
      : undefined);
  const [textInputValue, setTextInputValue] = useState(defaultDisplayName);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const optionsWrapperRef = useRef<HTMLDivElement>(null);
  const isSimpleVariant = variant === 'simple';
  const isSimpleLarge = variant === 'simple-large';
  const { isMoreThanTablet } = useMedia();
  const initialValueRef = useRef<string>();

  const displayText = useMemo(() => {
    if (isMulti && Array.isArray(value) && value.length > 0) {
      return value.map((item) => item.label).join(', ');
    }
    if (!isMulti && value && typeof value === 'object' && 'label' in value) {
      return value.label;
    }

    return displayName || '';
  }, [isMulti, value, displayName]);

  const showConfirmButton =
    !onlyChooseFromOptions &&
    !!textInputValue &&
    optionsVariant === 'none' &&
    parentOptionsVariant === 'modal';

  const showClearButton = useMemo(
    () => (!!onClear || clearable) && !!value,
    [!!onClear, value, clearable]
  );

  const handleClear = useCallback(
    (e?: MouseEvent<any>) => {
      if (!clearable) return;
      if (onClear) onClear();
      setTextInputValue('');
      onChangeInput?.(undefined);
      onSelect?.(isMulti ? [] : returnEmpty ? undefined : {});
    },
    [clearable, onClear, isMulti, onChangeInput, onSelect, returnEmpty]
  );
  const dropDownId = useId();

  useOutsideListener(wrapperRef, () => {
    if (optionsVariant !== 'dropdown') return;
    setTimeout(() => {
      setShow(false);
    }, 500);
  }, [dropDownId]);

  useEffect(() => {
    if (showPreview && !isMulti) {
      const firstOption = options[0];
      if (show && firstOption?.value) {
        const fullLabel = firstOption.label;
        const suggestionText = textInputValue
          ? fullLabel.slice(textInputValue.length)
          : fullLabel;
        setSuggestedCompletion(suggestionText);
      } else {
        setSuggestedCompletion(undefined);
      }
    }
  }, [
    show,
    options,
    textInputValue,
    showPreview,
    setSuggestedCompletion,
    isMulti,
  ]);

  const t = (str: string): string =>
    cp === 'dropdownSelect' && !doNotUseTranslation ? translate(str) : str;

  const onChange = (e: any) => {
    const inputText = e?.target?.value;
    if (!!checkIsValid && !checkIsValid(inputText)) return;

    if (!isMulti) {
      if (inputText !== undefined) {
        setShow(true);
        setTextInputValue(inputText);
        onChangeInput?.(inputText);
      }
      if (inputText === '') {
        setTextInputValue('');
        onSelect?.(returnEmpty ? undefined : {});
      }
    }
  };

  const onClickButton = (item: any) => {
    if (item?.disabled) return;
    if (item?.noClick) return;
    if (isMulti) {
      const isValueArray = Array.isArray(value);
      if (!isValueArray) {
        console.warn('isMulti value should be array', label, value);
      }

      const currentValues = isValueArray ? value : [];
      const isAlreadySelected = currentValues.some(
        (v: any) => v.value === item.value
      );

      let updateValue;
      if (isAlreadySelected) {
        updateValue = currentValues.filter((v: any) => v.value !== item.value);
      } else {
        updateValue = unionBy([...currentValues, item], 'value').slice(
          0,
          limit
        );
      }

      onSelect?.(updateValue as any);
    } else {
      onSelect?.(item);
      setTextInputValue(cleanInputAfterSelect ? '' : item?.label);
      setShow(false);
    }
    setKey(new Date().toISOString());
  };

  const onBlur = (e?: any) => {
    if (parentOnBlur) parentOnBlur?.(e);
  };

  const removeHandler = (item: any) => {
    const updatedValue = Array.isArray(value)
      ? value?.filter((i: any) => i.value !== item.value)
      : undefined;
    onSelect?.(updatedValue);
  };

  const onClick = () => {
    onClickProp?.();
    setShow((prev) => !prev);
  };

  useEffect(() => {
    setShow(isShow);
  }, [isShow]);

  useEffect(() => {
    setTextInputValue(displayName);
  }, [displayName]);

  const onClickRightIcon = () => {
    setShow((prev) => !prev);
  };

  const onClickOptionsWrapper = (event: React.MouseEvent<any>) =>
    preventClickHandler(event);

  useEffect(() => {
    initialValueRef.current = textInputValue;
    if (!isMoreThanTablet) return;
    const close = (e: Event) => {
      if (optionsWrapperRef?.current?.contains?.(e.target as HTMLElement))
        return;
      setShow(false);
    };
    if (show) {
      window.addEventListener('scroll', close, true);
    } else {
      window.removeEventListener('scroll', close);
    }

    return () => {
      window.removeEventListener('scroll', close);
    };
  }, [show, isMoreThanTablet]);

  const _handleConfirm = (value: string) => {
    const item = { label: value };
    onSelect?.(item);
    setShow(false);
  };

  const onKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Tab' && !isMulti) {
        e.preventDefault();
        const newInputText = options[0]?.label;
        setTextInputValue(newInputText);
        onChangeInput?.(newInputText);
        onSelect?.(options[0]);
        setShow(false);
      }
    },
    [options, onChangeInput, onSelect, isMulti]
  );

  useEffect(() => {
    if (isInModal && !isMoreThanTablet) {
      setTimeout(() => {
        wrapperRef.current?.querySelector?.('input')?.focus?.();
        onFocus?.(true);
      }, 0);
    }

    return () => {
      if (isInModal && !isMoreThanTablet) {
        document.body.focus();
        const activeElement = document.activeElement as HTMLElement;
        typeof activeElement?.blur === 'function' && activeElement.blur();
      }
    };
  }, []);

  return (
    <Flex ref={wrapperRef} className={className}>
      {renderContent ? (
        renderContent({ isOpen: show, onClick })
      ) : (
        <TextInput
          ref={inputRef}
          key={key + suggestedCompletion + options.length + options[0]?.label}
          label={label}
          helperText={helperText}
          onBlur={onBlur}
          onChange={onChange}
          value={t(isMulti ? displayText : textInputValue || '')}
          suggestedCompletionText={!isMulti ? suggestedCompletion : undefined}
          error={show ? undefined : error}
          visibleCharCounter={visibleCharCounter}
          disabled={disabled}
          readOnly={isMulti} // Make input read-only for multi-select
          rightIcon={
            rightIcon ||
            (visibleRightIcon ? (
              showConfirmButton ? (
                <IconButton
                  onClick={() => {
                    handleConfirm?.(textInputValue);
                  }}
                  size="md"
                  {...rightIconProps}
                  type="fas"
                  colorSchema="tertiary-transparent"
                  name="check-circle"
                />
              ) : showClearButton ? (
                <IconButton
                  onClick={handleClear}
                  type="far"
                  name="trash"
                  size="md"
                  disabled={disabledReadOnly || disabled}
                  {...rightIconProps}
                  className={cnj(
                    disabledReadOnly && classes.chevronDisabledReadonly,
                    rightIconProps.className
                  )}
                />
              ) : (
                <IconButton
                  onClick={onClickRightIcon}
                  type="far"
                  name={
                    show && options.length > 0 ? 'chevron-up' : 'chevron-down'
                  }
                  size="md"
                  disabled={disabledReadOnly || disabled}
                  {...rightIconProps}
                  className={cnj(
                    disabledReadOnly && classes.chevronDisabledReadonly,
                    rightIconProps.className
                  )}
                />
              )
            ) : undefined)
          }
          leftIcon={
            (value && !Array.isArray(value) && value.leftIcon) ||
            (leftIcon &&
              addClassToNode(
                (value && !Array.isArray(value) && value.leftIcon) || leftIcon,
                classes.input_leftIcon,
                leftIconClassName
              ))
          }
          style={cnj(classes.inputStyle, showEllipsis && '', style)}
          inputStyle={cnj(
            (isSimpleVariant || isSimpleLarge) && classes.simpleInputStyle,
            inputStyle,
            hasPriorityData
              ? classes.priorityInput
              : 'truncate max-w-[90%] whitespace-nowrap overflow-hidden text-ellipsis',
            isMulti && 'whitespace-normal overflow-visible text-wrap'
          )}
          inputWrapClassName={cnj(
            isSimpleVariant && classes.inputWrapClassName,
            isSimpleLarge && classes.isSimpleLarge,
            inputWrapClassName
          )}
          withRightIconClassName={cnj(
            (isSimpleVariant || isSimpleLarge) && classes.withRightIcon,
            isSimpleLarge && classes.simpleLarge,
            classes.widthOfRightIcon,
            withRightIconClassName
          )}
          onClick={onClick}
          isFocus={show || isFocus}
          onFocus={onFocus}
          trim={trim}
          editable={!isMulti && editable} // Disable editing for multi-select
          placeholder={placeholder}
          disabledRightIconPropagation={false}
          touched={show}
          rightIconClassName={rightIconClassName}
          maxLength={maxLength}
          autoComplete={autoComplete}
          disabledReadOnly={disabledReadOnly}
          {...textInputProps}
          variant="none"
          inputKey={key}
          labelStyle={hasPriorityData ? classes.labelStyles : ''}
          leftIconClassname={hasPriorityData ? classes.leftIcon : ''}
          onKeyDown={onKeyDown}
        />
      )}
      {show ? (
        optionsVariant === 'bottomsheet' ? (
          <BottomSheet
            open={show}
            onRequestClose={() => setShow(false)}
            modalElementClass={classes.bottomsheetwrapper}
          >
            <Flex className={classes.header}>
              <Typography size={16} height={19} font="700" color="smoke_coal">
                {t(label || '')}
              </Typography>
            </Flex>
            {renderOptions({
              container: cnj(
                classes.optionsWrapper,
                classes.bottomSheetOptionsWrapper
              ),
              optionBtn: classes.optionBtnBottomSheet,
            })}
          </BottomSheet>
        ) : optionsVariant === 'modal' ? (
          <FixedRightSideModal modalClassName={cnj(classes.modalRoot)}>
            <ModalHeaderSimple
              visibleHeaderDivider
              title={t(label || '')?.replace(t('(optional)'), '')}
              hideBack={false}
              backButtonProps={{
                onClick: (e: any) => {
                  e.target.value = initialValueRef.current;
                  onChange(e);
                  setShow(false);
                },
              }}
              noCloseButton
            />
            <Flex className={classes.modalBodyContainer}>
              <Flex className={classes.modalInputWrapper}>
                <AutoComplete
                  {...props}
                  optionsVariant="none"
                  parentOptionsVariant="modal"
                  handleConfirm={_handleConfirm}
                  isInModal
                />
              </Flex>
              {renderOptions({
                container: cnj(
                  classes.optionsWrapper,
                  classes.modalWrapperContainer
                ),
              })}
            </Flex>
          </FixedRightSideModal>
        ) : optionsVariant === 'dropdown' ? (
          <>
            {renderOptions({
              option: cnj(
                classes.options,
                !!helperText && classes.optionsWithHelperText,
                styles?.options
              ),
            })}
          </>
        ) : null
      ) : null}
    </Flex>
  );

  function renderOptions(classNames?: {
    option?: string;
    container?: string;
    optionBtn?: string;
  }) {
    function createPortalOrNot(nodes: ReactNode, parent: Element) {
      return isMoreThanTablet ? createPortal(nodes, parent) : nodes;
    }

    return (
      <Flex
        ref={(el: HTMLDivElement) => {
          if (optionsVariant !== 'dropdown' || !el) return;
          const container = el.querySelector('div');
          if (!container || elementIsVisibleInViewport(container)) return;
          container.style.top = 'unset';
          container.style.bottom = `${(wrapperRef.current?.clientHeight || 0) + 6}px`;
        }}
        className={cnj(
          classes.root,
          classes.show,
          classNames?.container,
          popperClassName
        )}
      >
        {options.length > 0 &&
          !!wrapperRef?.current &&
          createPortalOrNot(
            <Flex
              onClick={onClickOptionsWrapper}
              className={classNames?.option}
              ref={(el) => {
                if (!el || !isMoreThanTablet) return;
                optionsWrapperRef.current = el;
                setSpecs(optionsWrapperRef.current, wrapperRef?.current);
              }}
              id={dropDownId}
            >
              {options.map((item, index) => {
                const isSelected = isMulti
                  ? Array.isArray(value) &&
                    value.some((v: any) => v.value === item.value)
                  : value?.value === item.value;
                const schema =
                  isSelected && !isMulti
                    ? 'primary-blue'
                    : isDark
                      ? 'black'
                      : 'ghost';

                return renderItem ? (
                  <Ripple
                    key={optionKey?.(item, index) || `${item.value}_${index}`}
                    onClick={() => onClickButton(item)}
                    className={classes.ripple}
                    disabled={item?.disabled}
                  >
                    {renderItem({
                      item,
                      isSelected,
                      index,
                      text: textInputValue,
                    })}
                  </Ripple>
                ) : (
                  <Ripple
                    onClick={partial(onClickButton, item)}
                    key={
                      optionKey?.(item, index) ||
                      `${item.value}_${item.label}_${index}`
                    }
                    className={cnj(
                      classes.ripple,
                      isSelected && classes.borderRadius
                    )}
                  >
                    <Button
                      leftSvg={
                        item.leftIcon
                          ? addClassToNode(
                              item.leftIcon,
                              classes.option_leftIcon
                            )
                          : undefined
                      }
                      variant="large"
                      schema={schema}
                      key={schema}
                      label={!item?.helperText ? t(item.label) : undefined}
                      labelColor={
                        isSelected && !isMulti ? 'white' : 'smoke_coal'
                      }
                      labelProps={{
                        font: '400',
                        isTruncated: true,
                      }}
                      rightIcon={
                        isMulti
                          ? isSelected
                            ? 'check-square'
                            : 'square'
                          : isSelected && visibleCheck
                            ? 'check'
                            : undefined
                      }
                      rightColor={
                        isMulti
                          ? isSelected
                            ? 'primary-blue'
                            : 'smoke_coal'
                          : 'white'
                      }
                      rightSize={optionItemProps?.rightSize || 19}
                      rightIconClassName={classes.rightIcon}
                      {...optionItemProps}
                      className={cnj(
                        classes.optionBtn,
                        index !== 0 && !isSelected,
                        isSelected && !isMulti && classes.borderRadius,
                        classNames?.optionBtn,
                        optionItemClassName
                      )}
                    >
                      {item?.helperText && (
                        <>
                          <Flex className={classes.labelContainer}>
                            <OverflowTip
                              size={15}
                              height={21}
                              color="thirdText"
                              lineNumber={1}
                              isTruncated
                            >
                              {t(item.label)}
                            </OverflowTip>
                          </Flex>
                          <DividerDot />
                          <OverflowTip
                            size={12}
                            height={14}
                            color="secondaryDisabledText"
                            lineNumber={1}
                            isTruncated
                          >
                            {t(item.helperText)}
                          </OverflowTip>
                        </>
                      )}
                    </Button>
                  </Ripple>
                );
              })}
            </Flex>,
            document.body
          )}
      </Flex>
    );
  }
};

export default AutoComplete;

function setSpecs(el: HTMLDivElement, parentElement: HTMLDivElement) {
  if (!el || !parentElement) return;
  const width = parentElement?.clientWidth;
  const height = parentElement?.clientHeight;
  const { top, left } = getElementOffset({
    parentElement,
    elementHeight: el?.clientHeight,
    parentHeight: height,
  });
  el.style.left = `${left}px`;
  el.style.top = `${top + height}px`;
  el.style.width = `${width}px`;
  el.style.zIndex = '5000';
}

const getElementOffset = ({
  parentElement,
  elementHeight,
  parentHeight,
}: {
  parentElement: HTMLDivElement;
  elementHeight: number;
  parentHeight: number;
}) => {
  const rect = parentElement.getBoundingClientRect();
  const scrollLeft = window.scrollX;
  const scrollTop = window.scrollY;

  let top = rect.top + scrollTop;
  const left = rect.left + scrollLeft;

  if (
    rect.top + scrollTop + parentHeight + elementHeight >
    window.innerHeight
  ) {
    // Should open up to top
    top = top - parentHeight - elementHeight;
  }

  return {
    top,
    left,
  };
};
