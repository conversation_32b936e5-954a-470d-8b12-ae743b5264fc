import type { Key } from 'react';
import type React from 'react';

export interface FormFieldWrapperProps {
  name: string;
  component: any;
  label?: string;
  required?: boolean;
  privateable?: boolean;
  cp: any;
  onChange?: Function;
  trim?: boolean;
  forceVisibleError?: boolean;
  isFocused?: boolean;
  visibleOptionalLabel?: boolean;
}

export interface AsyncAutoCompleteFormProps
  extends AsyncAutoCompleteProps,
    Omit<FormFieldWrapperProps, 'cp' | 'component'> {}

export interface AutoCompleteOption {
  label: string;
  value?: any;
  helperText?: string;
  leftIcon?: ReactNode;
  image?: string;
  disabled?: boolean;
}

interface StyleProps {
  root?: string;
  options?: string;
  rightIcon?: string;
}

export type AutoCompleteProps<
  T extends AutoCompleteOption = AutoCompleteOption,
  M extends boolean = false,
> = {
  helperText?: string;
  label?: string;
  options: T[];
  onChangeInput?: Function;
  onBlur?: Function;
  onSelect?: Function;
  error?: string;
  style?: string;
  styles?: Partial<StyleProps>;
  disabled?: boolean;
  rightIcon?: ReactNode;
  leftIcon?: ReactNode;
  displayName?: string;
  isFocus?: boolean;
  onFocus?: Function;
  trim?: boolean;
  editable?: boolean;
  placeholder?: string;
  inputWrapClassName?: string;
  withRightIconClassName?: string;
  className?: string;
  inputStyle?: string;
  renderItem?: (args: any) => ReactNode;
  renderContent?: (args: any) => ReactNode;
  optionItemClassName?: string;
  visibleCheck?: boolean;
  translate?: (key: string) => string;
  isMulti?: M;
  limit?: number;
  name?: string;
  visibleRightIcon?: boolean;
  rightIconClassName?: string;
  rightIconProps?: any;
  maxLength?: number;
  autoComplete?: string;
  optionItemProps?: any;
  popperClassName?: string;
  variant?: 'simple' | 'form-input' | 'simple-large';
  visibleCharCounter?: boolean;
  cp?: string;
  doNotUseTranslation?: boolean;
  disabledReadOnly?: boolean;
  checkIsValid?: (value: string) => boolean;
  textInputProps?: Record<string, any>;
  optionsVariant?: 'dropdown' | 'modal' | 'bottomsheet' | 'none';
  onClick?: Function;
  isShow?: boolean;
  onlyChooseFromOptions?: boolean;
  parentOptionsVariant?: 'dropdown' | 'modal' | 'bottomsheet' | 'none';
  handleConfirm?: (value: string) => any;
  isInModal?: boolean;
  strict?: boolean;
  cleanInputAfterSelect?: boolean;
  showPreview?: boolean;
  hasPriorityData?: boolean;
  returnEmpty?: boolean;
  optionKey?: (item: T, index?: number) => Key;
  showEllipsis?: boolean;
  ref?: React.Ref<any>;
  onClear?: (value?: string) => void;
  leftIconClassName?: string;
  clearable?: boolean;
} & (M extends true
  ? {
      value?: T[];
    }
  : {
      value?: T;
    });

export interface AsyncAutoCompleteProps
  extends Omit<AutoCompleteProps, 'options'> {
  onChange?: Function;
  url?: any;
  normalizer?: Function;
  keywords?: string;
  apiFunc?: any;
  initSearchValue?: string;
  name: string;
  params?: {};
  showDropDownWithoutEnteringAnything?: boolean;
  onSelect?: Function | undefined;
  onOptions?: Function;
  hardRefetch?: boolean;
  accessToken?: string;
}
