export enum PlanName {
  FREE = 'FREE',
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE',
  TRIAL = 'TRIAL',
}

export enum FeatureType {
  PAID_PER_CALL = 'PAID_PER_CALL',
  RESPONSE_QUALIFICATION = 'RESPONSE_QUALIFICATION',
  CALL_RATE_LIMITING = 'CALL_RATE_LIMITING',
  NUMBER_OF_RECORDS_IN_RESPONSE = 'NUMBER_OF_RECORDS_IN_RESPONSE',
}

export type PlanRestrictionPayload = {
  status?: string | number; // "429" or 429 (optional)
  error?: string; // e.g., "PlanRateLimiterExceedException"
  featureName?: string; // e.g., "MEETING_YOURS"
  planAllow?: boolean; // whether plan allows the feature
  rateAllow?: boolean; // whether rate limit allows the call
  featureType?: FeatureType;
  featureValue?: unknown; // present when featureType !== CALL_RATE_LIMITING
  planName?: PlanName;
  price?: number;
  // other fields might exist, but we only consume the above safely
};

export enum FeatureName {
  BASIC = 'BASIC',
  PROJECT_CREATION = 'PROJECT_CREATION',
  JOB_CREATION = 'JOB_CREATION',
  CANDIDATE_SEARCH_LIMITATION = 'CANDIDATE_SEARCH_LIMITATION',
  CANDIDATE_CUSTOM_FIELD = 'CANDIDATE_CUSTOM_FIELD',
  CREATE_CANDIDATE = 'CREATE_CANDIDATE',
  INVITE_CANDIDATE = 'INVITE_CANDIDATE',
  ADD_CANDIDATE = 'ADD_CANDIDATE',
  TRACK_APPLICANT = 'TRACK_APPLICANT',
  AUTOMATED_RESPONSE_MOVEMENT = 'AUTOMATED_RESPONSE_MOVEMENT',
  AUTOMATED_RESPONSE_REJECTION = 'AUTOMATED_RESPONSE_REJECTION',
  AUTOMATED_RESPONSE_NOTE = 'AUTOMATED_RESPONSE_NOTE',
  AUTOMATED_RESPONSE_MESSAGE = 'AUTOMATED_RESPONSE_MESSAGE',
  AUTOMATED_RESPONSE_MEETING = 'AUTOMATED_RESPONSE_MEETING',
  AUTOMATED_RESPONSE_TODO = 'AUTOMATED_RESPONSE_TODO',
  AUTOMATED_RESPONSE_REPLY = 'AUTOMATED_RESPONSE_REPLY',
  PROVIDE_REVIEW_FOR_CANDIDATE = 'PROVIDE_REVIEW_FOR_CANDIDATE',
  PROJECT_SEARCH = 'PROJECT_SEARCH',
  JOB_SEARCH = 'JOB_SEARCH',
  GIVE_PAGE_ACCESS = 'GIVE_PAGE_ACCESS',
  REVOKE_PAGE_ACCESS = 'REVOKE_PAGE_ACCESS',
  ACCEPT_PAGE_ACCESS = 'ACCEPT_PAGE_ACCESS',
  DECLINE_PAGE_ACCESS = 'DECLINE_PAGE_ACCESS',
  ADD_EXTERNAL_CALENDAR = 'ADD_EXTERNAL_CALENDAR',
  REMOVE_EXTERNAL_CALENDAR = 'REMOVE_EXTERNAL_CALENDAR',
  CONNECT_TO_EXTERNAL_CALENDAR = 'CONNECT_TO_EXTERNAL_CALENDAR',
  DISCONNECT_TO_EXTERNAL_CALENDAR = 'DISCONNECT_TO_EXTERNAL_CALENDAR',
  AVAILABILITY = 'AVAILABILITY',
  MEETING_YOURS = 'MEETING_YOURS',
  MEETING_CANDIDATE = 'MEETING_CANDIDATE',
  MEETING_YOURS_GET = 'MEETING_YOURS_GET',
  MEETING_CANDIDATE_GET = 'MEETING_CANDIDATE_GET',
  MEETING_HEAD_GET = 'MEETING_HEAD_GET',
  MEETING_TEAM_MEMBER_GET = 'MEETING_TEAM_MEMBER_GET',
  NOTE_CANDIDATE = 'NOTE_CANDIDATE',
  NOTE_CANDIDATE_GET = 'NOTE_CANDIDATE_GET',
  NOTE_YOURS_GET = 'NOTE_YOURS_GET',
  NOTE_HEAD_GET = 'NOTE_HEAD_GET',
  NOTE_TEAM_MEMBER_GET = 'NOTE_TEAM_MEMBER_GET',
  AI_JOB_CREATION = 'AI_JOB_CREATION',
  TODO_YOURS = 'TODO_YOURS',
  TODO_CANDIDATE_GET = 'TODO_CANDIDATE_GET',
  TODO_YOURS_GET = 'TODO_YOURS_GET',
  TODO_HEAD_GET = 'TODO_HEAD_GET',
  TODO_TEAM_MEMBER_GET = 'TODO_TEAM_MEMBER_GET',
  PROJECT_YOURS_GET = 'PROJECT_YOURS_GET',
  PROJECT_TEAM_MEMBER_GET = 'PROJECT_TEAM_MEMBER_GET',
  PROJECT_HEAD_GET = 'PROJECT_HEAD_GET',
  JOB_PROJECT = 'JOB_PROJECT',
  JOB_YOURS_GET = 'JOB_YOURS_GET',
  JOB_TEAM_MEMBER_GET = 'JOB_TEAM_MEMBER_GET',
  JOB_HEAD_GET = 'JOB_HEAD_GET',
  APPLICANT_PROJECT = 'APPLICANT_PROJECT',
  APPLICANT_JOB = 'APPLICANT_JOB',
  APPLICANT_YOURS_GET = 'APPLICANT_YOURS_GET',
  APPLICANT_TEAM_MEMBER_GET = 'APPLICANT_TEAM_MEMBER_GET',
  APPLICANT_HEAD_GET = 'APPLICANT_HEAD_GET',
  CANDIDATE_PROJECT = 'CANDIDATE_PROJECT',
  CANDIDATE_JOB = 'CANDIDATE_JOB',
  CANDIDATE_YOURS_GET = 'CANDIDATE_YOURS_GET',
  CANDIDATE_TEAM_MEMBER_GET = 'CANDIDATE_TEAM_MEMBER_GET',
  CANDIDATE_HEAD_GET = 'CANDIDATE_HEAD_GET',
  REVIEW_CANDIDATE = 'REVIEW_CANDIDATE',
  REVIEW_JOB = 'REVIEW_JOB',
  REVIEW_YOURS_GET = 'REVIEW_YOURS_GET',
  REVIEW_TEAM_MEMBER_GET = 'REVIEW_TEAM_MEMBER_GET',
  REVIEW_HEAD_GET = 'REVIEW_HEAD_GET',
  ACTIVITY_PROJECT = 'ACTIVITY_PROJECT',
  ACTIVITY_JOB = 'ACTIVITY_JOB',
  ACTIVITY_CANDIDATE = 'ACTIVITY_CANDIDATE',
  ACTIVITY_YOURS_GET = 'ACTIVITY_YOURS_GET',
  ACTIVITY_TEAM_MEMBER_GET = 'ACTIVITY_TEAM_MEMBER_GET',
  ACTIVITY_HEAD_GET = 'ACTIVITY_HEAD_GET',
  COLLABORATION_PROJECT = 'COLLABORATION_PROJECT',
  COLLABORATION_JOB = 'COLLABORATION_JOB',
  SUPPORT = 'SUPPORT',
  MEETING_TEMPLATE = 'MEETING_TEMPLATE',
  DOCUMENT_MANAGEMENT = 'DOCUMENT_MANAGEMENT',
  THREADS_JOB_LEVEL_MESSAGING = 'THREADS_JOB_LEVEL_MESSAGING',
  THREADS_CANDIDATE_LEVEL = 'THREADS_CANDIDATE_LEVEL',
  THREADS_YOURS = 'THREADS_YOURS',
  THREADS_TEAM_MEMBER_LEVEL = 'THREADS_TEAM_MEMBER_LEVEL',
  THREADS_COMPANY_LEVEL = 'THREADS_COMPANY_LEVEL',
  SEE_SIMILAR_CANDIDATE = 'SEE_SIMILAR_CANDIDATE',
  COMPARE_CANDIDATE = 'COMPARE_CANDIDATE',
  SHARE_JOBS_WITH_VENDORS = 'SHARE_JOBS_WITH_VENDORS',
  SUBMIT_CANDIDATES = 'SUBMIT_CANDIDATES',
  SEARCH_COMPANIES = 'SEARCH_COMPANIES',
  SAVE_SEARCH_RESULTS = 'SAVE_SEARCH_RESULTS',
  PIPELINE = 'PIPELINE',
  DIRECT_MESSAGE = 'DIRECT_MESSAGE',
  ONLINE_STATUS = 'ONLINE_STATUS',
  CALENDAR = 'CALENDAR',
  TODO_PROJECT_GET = 'TODO_PROJECT_GET',
  MEETING_PROJECT_GET = 'MEETING_PROJECT_GET',
  THREADS_HEAD_LEVEL = 'THREADS_HEAD_LEVEL',
  CANDIDATE_SEARCH_FILTER = 'CANDIDATE_SEARCH_FILTER',
  LIST_SEARCH_RESULTS = 'LIST_SEARCH_RESULTS',
}
