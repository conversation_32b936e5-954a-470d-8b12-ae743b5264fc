import { isBusinessApp } from '@shared/utils/getAppEnv';
import pageApi from 'shared/utils/api/page';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import type { PagePlanInfoResponse } from 'shared/utils/api/page';

const useGetPagePlanInfo = (config = {}) =>
  useReactQuery<PagePlanInfoResponse>({
    action: {
      key: [QueryKeys.getPagePlanInfo],
      apiFunc: pageApi.getPagePlanInfo,
    },
    config: {
      enabled: isBusinessApp,
      ...config,
    },
  });

export default useGetPagePlanInfo;
