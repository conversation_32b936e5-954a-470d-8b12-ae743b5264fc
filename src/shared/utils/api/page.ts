import beforeCacheUserInfo from 'shared/utils/normalizers/beforeCacheUserInfo';
import Endpoints from '../constants/endpoints';
import { pageEndpoints } from '../constants/servicesEndpoints';
import { getPortal } from '../getAppEnv';
import beforeCachePageDetail from '../normalizers/beforeCachePageDetail';
import { accessibilitiesNormalizer } from '../normalizers/pageAccessibilityNormalizer';
import {
  ActivePlanNormalizer,
  billingNormalizer,
  PlanNormalizer,
} from '../normalizers/plansNormalizer';
import request from '../toolkit/request';
import type { AppPortal } from '../getAppEnv';
import type {
  PageFullAccessibilitiesReturnType,
  PageApiResponseType,
  PageCategoryType,
  PageCompanySizeType,
  BEPagePlan,
  BEPlan,
  BEBilling,
  PlanTimeSpan,
  BeforeCachePageDetailType,
} from 'shared/types/page';
import type { PaginateParamType } from 'shared/types/params';
import type { ListResponse, PaginateResponse } from 'shared/types/response';

export type GetPageDetailType = {
  username?: string;
  accessToken?: string;
  pageId?: string;
  isLightVersion?: boolean;
};

export const getPageDetail = async ({
  params,
  accessToken,
}: {
  params: GetPageDetailType;
  accessToken?: string;
}): Promise<BeforeCachePageDetailType> => {
  if (!params?.username && !params?.pageId) {
    throw new Error('pagename or pageId is required');
  }
  const { username, pageId, isLightVersion } = params;
  const url = isLightVersion
    ? Endpoints.App.Page.getLight
    : Endpoints.App.Page.get;
  const { data } = await request.get(url, {
    accessToken,
    params: {
      pageId,
      username,
      containsCroppedHeaderImageLink: true,
    },
  });

  return beforeCachePageDetail(data);
};

export const createPage = async (pageData: any) => {
  const { data } = await request.post(Endpoints.App.Page.base, pageData);

  return data;
};

const beforeCacheMyPages = (data: any) =>
  data?.reduce?.((prev: any, curr: any) => {
    const existItem = prev.find((i: any) => i.page?.id === curr.page?.id);

    return existItem
      ? prev.map((item: any) => ({
          ...item,
          roles: [...existItem.roles, curr.role],
        }))
      : [...prev, { ...curr, roles: [curr.role] }];
  }, []);

export const getMyPages = async (): Promise<any> => {
  const response = await request.get(Endpoints.App.Page.myPages);

  return beforeCacheMyPages(response?.data);
};

export const getMyOwnPages = async (): Promise<Array<PageApiResponseType>> => {
  const { data } = await request.get(pageEndpoints.myOwnPages);

  return data?.map(beforeCachePageDetail);
};

export const updatePage = async (pageDetails: any): Promise<any> => {
  const url = `${Endpoints.App.Page.base}/${pageDetails?.id}`;
  const { data } = await request.put(url, pageDetails);

  return data;
};

export const updatePageTitle = async ({ id, title }: any): Promise<any> => {
  const url = `${Endpoints.App.Page.base}/${id}/title`;
  const { data } = await request.put(url, {
    title,
  });

  return data;
};

export const updatePageLocations = async ({
  id,
  locations,
}: any): Promise<any> => {
  const url = `${Endpoints.App.Page.base}/${id}/location`;
  const { data } = await request.put(url, {
    locations,
  });

  return data;
};

export const getFilteredPages = async (
  params: PaginateParamType & {
    industryLookupId?: string;
    text?: string;
  }
): Promise<PaginateResponse<PageApiResponseType>> => {
  const { data } = await request.get(pageEndpoints.filterPages, {
    params,
  });

  return data;
};

export const givePageAccess = async (accessData: any): Promise<any> => {
  const { data } = await request.put(Endpoints.App.Page.pageMember, accessData);

  return data;
};

export const givePortalAccess = async (accessData: any): Promise<any> => {
  const { data } = await request.put(
    Endpoints.App.Page.portalAccess,
    accessData
  );

  return data;
};

export const transferOwnership = async (accessData: any): Promise<any> => {
  const { data } = await request.put(
    Endpoints.App.Page.transferOwnership,
    accessData
  );

  return data;
};

export const getPageMembers = async ({ params }: any): Promise<any> => {
  const pageId = params?.pageId;
  const { data } = await request.get(Endpoints.App.Page.getMembers, {
    params: { pageId },
  });

  return data?.map(({ id, role, profileInfo, page, status }: any) => ({
    id,
    role,
    user: {
      fullName: `${profileInfo?.name} ${profileInfo?.surname}`,
      username: `@${profileInfo?.username}`,
      id: profileInfo?.userId,
      image: profileInfo?.croppedImageUrl,
      job: profileInfo?.occupationName,
    },
    pageId: page.id,
    status,
  }));
};

export const deletePageMember = async (deleteData: any): Promise<void> => {
  const { data: resData } = await request.delete(
    Endpoints.App.Page.pageMember,
    deleteData
  );

  return resData;
};
export const deletePortalMember = async (deleteData: any): Promise<void> => {
  const { data: resData } = await request.delete(
    Endpoints.App.Page.portalMember,
    deleteData
  );

  return resData;
};

export const setTitle = async ({
  id,
  title,
}: {
  id: string;
  title: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.title(id), { title });

  return data;
};

export const setPageName = async ({
  id,
  username,
}: {
  id: string;
  username: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.pageName(id), {
    username,
  });

  return data;
};

export const setCategory = async ({
  id,
  category,
}: {
  id: string;
  category: PageCategoryType;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.category(id), { category });

  return data;
};

export const setIndustry = async ({
  id,
  industryLookupId,
  industryName,
}: {
  id: string;
  industryLookupId: string;
  industryName: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.industry(id), {
    industryLookupId,
    industryName,
  });

  return data;
};

export const setDescription = async ({
  id,
  description,
}: {
  id: string;
  description: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.description(id), {
    description,
  });

  return data;
};

export const setEstablishmentDate = async ({
  id,
  establishmentDate,
}: {
  id: string;
  establishmentDate: Date;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.establishmentDate(id), {
    establishmentDate,
  });

  return data;
};

export const setCompanySize = async ({
  id,
  companySize,
}: {
  id: string;
  companySize: PageCompanySizeType;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.companySize(id), {
    companySize,
  });

  return data;
};

export const setPublish = async ({ id }: { id: string }): Promise<void> => {
  const { data } = await request.put(pageEndpoints.publish(id));

  return data;
};

export const setUnpublish = async ({ id }: { id: string }): Promise<void> => {
  const { data } = await request.put(pageEndpoints.unpublish(id));

  return data;
};

export const setAdultContent = async ({
  id,
  value,
}: {
  id: string;
  value: boolean;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.adultContent(id), { value });

  return data;
};

export const deletePage = async ({
  id,
  ...rest
}: {
  id: string;
}): Promise<void> => {
  const { data } = await request.delete(pageEndpoints.deleteEp(id), {
    data: { ...rest },
  });

  return data;
};

export const setPageEmail = async ({
  id,
  email,
}: {
  id: string;
  email: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.email(id), { email });

  return data;
};

export const setPagePhone = async ({
  id,
  phone,
}: {
  id: string;
  phone: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.phone(id), { phone });

  return data;
};

export const setPageLink = async ({
  id,
  link,
}: {
  id: string;
  link: string;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.link(id), { link });

  return data;
};

export const setPageLocations = async ({
  id,
  locations,
}: {
  id: string;
  locations: Array<any>;
}): Promise<void> => {
  const { data } = await request.put(pageEndpoints.locations(id), {
    locations,
  });

  return data;
};

export const acceptMemberShip = async (memberData: any): Promise<void> => {
  const { data } = await request.put(
    pageEndpoints.acceptMemberShip,
    memberData
  );

  return data;
};

export const declineMemberShip = async (putData: any): Promise<void> => {
  const { data } = await request.put(pageEndpoints.declineMemberShip, putData);

  return data;
};

export const getPageDeleteStatus = async ({
  params,
}: {
  params: GetPageDetailType;
}): Promise<boolean> => {
  if (!params?.username) {
    throw new Error('pagename or pageId is required to getPageDeleteStatus');
  }
  const { data } = await request.get<{ pageId: string }>(
    Endpoints.App.Page.deleteStatus,
    {
      params,
    }
  );

  return Boolean(data?.pageId);
};
export const verifyPageDelete = async ({ token }): Promise<any> => {
  const { data } = await request.delete(pageEndpoints.verifyPageDelete(token));

  return data;
};

export const getFullAccessibilityGuide = async ({
  accessToken,
}: {
  accessToken: string;
}) => {
  const { data } = await request.get<PageFullAccessibilitiesReturnType>(
    pageEndpoints.fullAccessibilityGuide,
    {
      accessToken,
    }
  );

  return (data?.accessibilities || [])?.map(accessibilitiesNormalizer);
};

export const getPageAccessibilities = async ({
  containsPendingAndDeclined,
  accessToken,
}: {
  accessToken: string;
  containsPendingAndDeclined: boolean;
}): Promise<any> => {
  const { data } = await request.get(pageEndpoints.pageAccessibilities, {
    accessToken,
    params: { containsPendingAndDeclined },
  });

  return data?.content?.map(({ profileInfo, userId, ...item }) => ({
    ...item,
    userId,
    user: beforeCacheUserInfo({
      ...profileInfo,
      id: userId,
    }),
    pageMemberships: item.accessibilities?.[0]?.pageMemberships?.filter(
      (i) => i.userId === userId
    ),
    portalAccesses: item.accessibilities?.[0]?.portalAccesses?.filter(
      (i) => i.userId === userId
    ),
  }));
};

export const getPagePlans = async ({
  pageId,
  ...params
}: {
  pageId: string;
}): Promise<BEPagePlan[]> => {
  const { data } = await request.get<ListResponse<BEPagePlan>>(
    pageEndpoints.plans(pageId),
    params
  );

  return data?.content;
};

export const getPlansList = async (params: {
  portal: AppPortal;
  timeSpan: PlanTimeSpan;
}) => {
  const { data } = await request.get<BEPlan[]>(pageEndpoints.planFeatures, {
    params,
  });

  return ActivePlanNormalizer(data)?.map(PlanNormalizer);
};

export const getPlanBillingList = async (params: PaginateParamType) => {
  const { data } = await request.get<PaginateResponse<BEBilling>>(
    pageEndpoints.billing,
    {
      params: { ...params },
    }
  );

  return { ...data, content: data?.content?.map(billingNormalizer) };
};

export const getPlanBillingDetails = async (params: { id: string }) => {
  const { data } = await request.get<BEBilling[]>(
    pageEndpoints.billingDetails(params?.id)
  );

  return data?.map(billingNormalizer);
};
type EnrollPlanPayload = {
  page: string;
  planName: string;
  timeSpan: string;
  portalName: string;
};

interface EnrollPlanResponse {
  endDate: null;
  id: string;
  invoiceNumber: null;
  last4digits: null;
  numberOfSeats: '1';
  paymentDate: null;
  planName: null;
  price: string;
  remainingDays: null;
  requestType: 'NEW_PLAN';
  status: null;
  taxAmount: string;
  timeSpan: null;
  pricePerSeat: string;
  discount: string;
}

export type PagePlanInfoResponse = {
  enrolledPlanInfoList: Array<{
    planName: string;
    portalName: string;
    timeSpan: string;
  }>;
  paidPerCallFeatureInfoList: Array<{
    featureName: string;
    price: number;
  }>;
  activePlan: {
    planName: string;
    portalName: string;
    timeSpan: string;
    noOfPurchaseSeat: string;
    noOfActiveSeat: string;
  };
};

export const enrollIntoPlan = async (params: EnrollPlanPayload) => {
  const { data } = await request.post<EnrollPlanResponse>(
    pageEndpoints.enrollPlan,
    params
  );

  return {
    discount: data.discount,
    requestType: data.requestType,
    numberOfSeats: Number(data.numberOfSeats),
    price: Number(data?.price),
    taxAmount: Number(data?.taxAmount),
    pricePerSeat: Number(data?.pricePerSeat),
    id: data?.id,
  };
};

export const cancelPlan = async (params: EnrollPlanPayload) => {
  const { data } = await request.put<BEPlan>(pageEndpoints.cancelPlan, {
    ...params,
  });
  return data;
};

export const getPagePlanInfo = async (): Promise<PagePlanInfoResponse> => {
  const { data } = await request.get<PagePlanInfoResponse>(
    pageEndpoints.planInfo
  );
  const activePlan = data.enrolledPlanInfoList.find(
    (plan) => plan.portalName === getPortal().toUpperCase()
  );
  return {
    ...data,
    activePlan: {
      ...activePlan,
      timeSpan:
        activePlan?.timeSpan === 'UNLIMITED' ? 'MONTHLY' : activePlan?.timeSpan,
    },
  };
};

export const applyDiscount = async ({
  requestId,
  discountCode,
}: {
  requestId: string;
  discountCode: string;
}): Promise<any> => {
  const { data } = await request.post(pageEndpoints.applyDiscount(requestId), {
    value: discountCode,
  });

  return data;
};

export const exports = {
  acceptMemberShip,
  declineMemberShip,
  updatePage,
  getPageDetail,
  getMyPages,
  getMyOwnPages,
  updatePageTitle,
  updatePageLocations,
  getFilteredPages,
  givePageAccess,
  givePortalAccess,
  transferOwnership,
  deletePortalMember,
  getPageMembers,
  deletePageMember,
  setTitle,
  setPageName,
  setIndustry,
  setDescription,
  setEstablishmentDate,
  setCompanySize,
  setPublish,
  setUnpublish,
  setAdultContent,
  deletePage,
  setPageEmail,
  setPagePhone,
  setPageLink,
  setPageLocations,
  createPage,
  getPageDeleteStatus,
  verifyPageDelete,
  getFullAccessibilityGuide,
  getPageAccessibilities,
  getPagePlans,
  getPlanBillingList,
  getPlanBillingDetails,
  enrollIntoPlan,
  cancelPlan,
  getPagePlanInfo,
  applyDiscount,
};

export default exports;
