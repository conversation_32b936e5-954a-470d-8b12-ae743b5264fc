import { isPromise } from 'formik';
import paymentEndPoints from '../constants/servicesEndpoints/services/payment';
import request from '../toolkit/request';

type SavedCard = {
  id: string;
  name: string;
  last4digits: string;
  cardType: string | null;
  label?: string;
  value?: string;
};
export type BEPaypentTokenResponse = {
  token: string;
  methods: SavedCard[];
};

export const getPaymentClientToken = async () => {
  const { data } = await request.get<BEPaypentTokenResponse>(
    paymentEndPoints.token
  );

  return {
    ...data,
    methods: data?.methods?.map((item) => ({
      ...item,
      label: item.name,
      value: item.id,
    })),
  };
};

export const proceedPayment = async <T>(payload: T | Promise<T>) => {
  if (isPromise(payload)) payload = await payload;
  const { data } = await request.post(paymentEndPoints.charge, payload);

  return data;
};
