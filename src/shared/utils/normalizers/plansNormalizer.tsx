import { type MouseEvent, type FC } from 'react';
import {
  Golden<PERSON>ogo,
  PlatinumLogo,
  SilverLogo,
  StyledLogo,
} from '@shared/svg/LogoIcon';
import { type BEBilling, type BEPlan } from '@shared/types/page';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import { type ColorsKeys } from '@shared/uikit/types';
import Typography from '@shared/uikit/Typography';
import { type appPortals } from '../getAppEnv';

export const plans = {
  FREE: {
    id: 1,
    title: 'Free',
    label: 'Free',
    Logo: StyledLogo,
    priceUnit: undefined,
    color: 'brand',
  },
  STANDARD: {
    id: 2,
    title: 'Standard',
    label: 'Standard',
    Logo: SilverLogo,
    color: 'secondaryDisabledText',
    priceUnit: 'per_seat_per_month',
  },
  PREMIUM: {
    id: 3,
    title: 'Premium',
    label: 'Premium',
    Logo: GoldenLogo,
    color: 'warning',
    priceUnit: 'Per Seat/Month',
  },
  ENTERPRISE: {
    id: 4,
    title: 'Enterprise',
    label: 'Enterprise',
    Logo: PlatinumLogo,
    color: 'colorIconForth2',
    priceUnit: 'Per Seat/Month',
  },
} as const;

export interface Plan {
  id?: number;
  title: string;
  label: string;
  Logo: FC;
  price: number;
  priceUnit?: string;
  isActive: boolean;
  features: {
    description: string;
    Render?: string | JSX.Element;
    label: string;
  }[];
  color?: ColorsKeys;
  portal?: (typeof appPortals)[number][];
  onSelect?: (event?: MouseEvent<any>) => void;
}

const NotAvailable = () => (
  <Flex className="w-24 h-24 rounded-full bg-techGray_20">
    <Icon
      color="primaryDisabledText"
      size={15}
      className="m-auto"
      name="times"
    />
  </Flex>
);

const WithText = ({ text }: { text: string }) => (
  <Flex flexDir="row" className="gap-4">
    <Flex className="w-24 h-24 mr-8 rounded-full bg-techGray_20">
      <Icon color="smoke_coal" size={15} className="m-auto" name="check" />
    </Flex>
    <Typography height={24} color="secondaryDisabledText">
      {text}
    </Typography>
  </Flex>
);

const NormalComponent = (value: string) => {
  if (value === '-') return <NotAvailable />;

  return <WithText text={value} />;
};

const WithTextTransform =
  // eslint-disable-next-line react/display-name
  (transform: (val: string) => string) => (value: string) => (
    <WithText text={transform(value)} />
  );

const WithTextDT =
  // eslint-disable-next-line react/display-name
  (transform: (val: string) => string | false) => (value: string) => {
    const text = transform(value);
    if (!text) return <NotAvailable />;

    return <WithText text={text} />;
  };

const components: Record<
  string,
  {
    featureName: string;
    Component: (value: string) => JSX.Element;
    value?: any;
    hide?: boolean;
  }
> = {
  PROJECT_CREATION: {
    featureName: 'PROJECT_CREATION',
    value: 'Unlimited',
    Component: WithTextTransform((val) => `$${val}/job`),
  },
  JOB_CREATION: {
    featureName: 'JOB_CREATION',
    value: '99',
    Component: WithTextTransform((val) => `$${val}/job`),
  },
  CANDIDATE_SEARCH_LIMITATION: {
    featureName: 'CANDIDATE_SEARCH_LIMITATION',
    value: '3',
    Component: NormalComponent,
  },
  SEE_SIMILAR_CANDIDATE: {
    featureName: 'SEE_SIMILAR_CANDIDATE',
    value: '3',
    Component: NormalComponent,
  },
  PROVIDE_REVIEW_FOR_CANDIDATE: {
    featureName: 'PROVIDE_REVIEW_FOR_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  COMPARE_CANDIDATE: {
    featureName: 'COMPARE_CANDIDATE',
    value: '3',
    Component: NormalComponent,
  },
  SAVE_SEARCH_RESULTS: {
    featureName: 'SAVE_SEARCH_RESULTS',
    value: '3',
    Component: WithTextDT((val) =>
      val === '0' || val === '-'
        ? false
        : val === 'Unlimited'
          ? val
          : val === 'limited'
            ? `Limited to 10`
            : `Limited to ${val}`
    ),
  },
  CANDIDATE_CUSTOM_FIELD: {
    featureName: 'CANDIDATE_CUSTOM_FIELD',
    value: '-',
    Component: NormalComponent,
  },
  INVITE_CANDIDATE: {
    featureName: 'INVITE_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  CREATE_CANDIDATE: {
    featureName: 'CREATE_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  ADD_CANDIDATE: {
    featureName: 'ADD_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  TRACK_APPLICANT: {
    featureName: 'TRACK_APPLICANT',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  SUBMIT_CANDIDATES: {
    featureName: 'SUBMIT_CANDIDATES',
    value: '3',
    Component: WithTextDT((val) =>
      val === '-' ? false : 'Comprehensive VMS tool'
    ),
  },
  SHARE_JOBS_WITH_VENDORS: {
    featureName: 'SHARE_JOBS_WITH_VENDORS',
    value: '3',
    Component: WithTextDT((val) =>
      val === '-' ? false : 'Comprehensive VMS tool'
    ),
  },
  AUTOMATED_RESPONSE_MOVEMENT: {
    featureName: 'AUTOMATED_RESPONSE_MOVEMENT',
    value: '-',
    Component: WithTextDT((val) => (val === '-' ? false : 'enabled')),
  },
  AUTOMATED_RESPONSE_REJECTION: {
    featureName: 'AUTOMATED_RESPONSE_REJECTION',
    value: '-',
    Component: WithTextDT((val) => (val === '-' ? false : 'enabled')),
  },
  AUTOMATED_RESPONSE_NOTE: {
    featureName: 'AUTOMATED_RESPONSE_NOTE',
    value: '-',
    Component: WithTextDT((val) => (val === '-' ? false : 'enabled')),
  },
  AUTOMATED_RESPONSE_MESSAGE: {
    featureName: 'AUTOMATED_RESPONSE_MESSAGE',
    value: '-',
    Component: WithTextDT((val) => (val === '-' ? false : 'enabled')),
  },
  AUTOMATED_RESPONSE_TODO: {
    featureName: 'AUTOMATED_RESPONSE_TODO',
    value: '-',
    Component: WithTextDT((val) => (val === '-' ? false : 'enabled')),
  },
  AUTOMATED_RESPONSE_REPLY: {
    featureName: 'AUTOMATED_RESPONSE_REPLY',
    value: '-',
    Component: WithTextDT((val) => (val === '-' ? false : 'enabled')),
  },
  MEETING_TEMPLATE: {
    featureName: 'MEETING_TEMPLATE',
    value: '1',
    Component: WithTextDT((val) =>
      val === '0' || val === '-'
        ? false
        : val === 'Unlimited'
          ? val
          : `Limited to ${val}`
    ),
  },
  PROJECT_SEARCH: {
    featureName: 'PROJECT_SEARCH',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  JOB_SEARCH: {
    featureName: 'JOB_SEARCH',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  SEARCH_COMPANIES: {
    featureName: 'SEARCH_COMPANIES',
    value: '3',
    Component: NormalComponent,
  },
  GIVE_PAGE_ACCESS: {
    featureName: 'GIVE_PAGE_ACCESS',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  ACCEPT_PAGE_ACCESS: {
    featureName: 'ACCEPT_PAGE_ACCESS',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  DECLINE_PAGE_ACCESS: {
    featureName: 'DECLINE_PAGE_ACCESS',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  REVOKE_PAGE_ACCESS: {
    featureName: 'REVOKE_PAGE_ACCESS',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  ADD_EXTERNAL_CALENDAR: {
    featureName: 'ADD_EXTERNAL_CALENDAR',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  REMOVE_EXTERNAL_CALENDAR: {
    featureName: 'REMOVE_EXTERNAL_CALENDAR',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  CONNECT_TO_EXTERNAL_CALENDAR: {
    featureName: 'CONNECT_TO_EXTERNAL_CALENDAR',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  DISCONNECT_TO_EXTERNAL_CALENDAR: {
    featureName: 'DISCONNECT_TO_EXTERNAL_CALENDAR',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  AVAILABILITY: {
    featureName: 'AVAILABILITY',
    value: 'Unlimited',
    Component: WithTextDT((val) =>
      val === '-' ? false : val === 'Unlimited' ? 'Unlimited' : '1/day'
    ),
  },
  MEETING_YOURS: {
    featureName: 'MEETING_YOURS',
    value: 'Unlimited',
    Component: WithTextDT((val) =>
      val === '-' ? false : val === 'Unlimited' ? 'Unlimited' : '1/day'
    ),
  },
  TODO_YOURS: {
    featureName: 'TODO_YOURS',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  TODO_CANDIDATE: {
    featureName: 'TODO_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  DOCUMENT_MANAGEMENT: {
    featureName: 'DOCUMENT_MANAGEMENT',
    value: '3',
    Component: WithTextDT((val) =>
      val === '0' || val === '-'
        ? false
        : val === 'Unlimited'
          ? val
          : `Limited to ${val}`
    ),
  },
  AI_JOB_CREATION: {
    featureName: 'AI_JOB_CREATION',
    value: 'Limited',
    Component: WithTextDT((val) =>
      val === '0' || val === '-'
        ? false
        : val === 'Unlimited'
          ? val
          : `Limited to ${val}/month`
    ),
  },
  TODO_YOURS_GET: {
    featureName: 'TODO_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  TODO_CANDIDATE_GET: {
    featureName: 'TODO_CANDIDATE_GET',
    value: '3',
    Component: NormalComponent,
    hide: true,
  },
  TODO_TEAM_MEMBER_GET: {
    featureName: 'TODO_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  TODO_HEAD_GET: {
    featureName: 'TODO_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  NOTE_CANDIDATE_GET: {
    featureName: 'NOTE_CANDIDATE_GET',
    value: '3',
    Component: NormalComponent,
  },
  NOTE_CANDIDATE: {
    featureName: 'NOTE_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
    hide: true,
  },
  NOTE_YOURS_GET: {
    featureName: 'NOTE_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  NOTE_TEAM_MEMBER_GET: {
    featureName: 'NOTE_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  NOTE_HEAD_GET: {
    featureName: 'NOTE_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  MEETING_CANDIDATE: {
    featureName: 'MEETING_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  MEETING_CANDIDATE_GET: {
    featureName: 'MEETING_CANDIDATE_GET',
    value: '3',
    Component: NormalComponent,
  },
  MEETING_YOURS_GET: {
    featureName: 'MEETING_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  MEETING_TEAM_MEMBER_GET: {
    featureName: 'MEETING_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  MEETING_HEAD_GET: {
    featureName: 'MEETING_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  THREADS_JOB_LEVEL_MESSAGING: {
    featureName: 'THREADS_JOB_LEVEL_MESSAGING',
    value: '3',
    Component: NormalComponent,
  },
  THREADS_CANDIDATE_LEVEL: {
    featureName: 'THREADS_CANDIDATE_LEVEL',
    value: '3',
    Component: NormalComponent,
  },
  THREADS_YOURS: {
    featureName: 'THREADS_YOURS',
    value: '3',
    Component: NormalComponent,
  },
  THREADS_TEAM_MEMBER_LEVEL: {
    featureName: 'THREADS_TEAM_MEMBER_LEVEL',
    value: '3',
    Component: NormalComponent,
  },
  THREADS_COMPANY_LEVEL: {
    featureName: 'THREADS_COMPANY_LEVEL',
    value: '3',
    Component: NormalComponent,
  },
  PROJECT_YOURS_GET: {
    featureName: 'PROJECT_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  PROJECT_TEAM_MEMBER_GET: {
    featureName: 'PROJECT_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  PROJECT_HEAD_GET: {
    featureName: 'PROJECT_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  JOB_PROJECT: {
    featureName: 'JOB_PROJECT',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  JOB_YOURS_GET: {
    featureName: 'JOB_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  JOB_TEAM_MEMBER_GET: {
    featureName: 'JOB_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  JOB_HEAD_GET: {
    featureName: 'JOB_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  APPLICANT_PROJECT: {
    featureName: 'APPLICANT_PROJECT',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  APPLICANT_JOB: {
    featureName: 'APPLICANT_JOB',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  APPLICANT_YOURS_GET: {
    featureName: 'APPLICANT_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  APPLICANT_TEAM_MEMBER_GET: {
    featureName: 'APPLICANT_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  APPLICANT_HEAD_GET: {
    featureName: 'APPLICANT_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  CANDIDATE_PROJECT: {
    featureName: 'CANDIDATE_PROJECT',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  CANDIDATE_JOB: {
    featureName: 'CANDIDATE_JOB',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  CANDIDATE_YOURS_GET: {
    featureName: 'CANDIDATE_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  CANDIDATE_TEAM_MEMBER_GET: {
    featureName: 'CANDIDATE_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  CANDIDATE_HEAD_GET: {
    featureName: 'CANDIDATE_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  REVIEW_JOB: {
    featureName: 'REVIEW_JOB',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  REVIEW_CANDIDATE: {
    featureName: 'REVIEW_CANDIDATE',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  REVIEW_YOURS_GET: {
    featureName: 'REVIEW_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  REVIEW_TEAM_MEMBER_GET: {
    featureName: 'REVIEW_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  REVIEW_HEAD_GET: {
    featureName: 'REVIEW_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },
  ACTIVITY_PROJECT: {
    featureName: 'ACTIVITY_PROJECT',
    value: 'Limited',
    Component: WithTextDT((val) =>
      val === '-' ? false : val === 'Unlimited' ? 'Unlimited' : 'Limited to 8'
    ),
  },
  ACTIVITY_CANDIDATE: {
    featureName: 'ACTIVITY_CANDIDATE',
    value: 'Limited',
    Component: WithTextDT((val) =>
      val === '-' ? false : val === 'Unlimited' ? 'Unlimited' : 'Limited to 8'
    ),
  },
  ACTIVITY_JOB: {
    featureName: 'ACTIVITY_JOB',
    value: 'Unlimited',
    Component: WithTextDT((val) =>
      val === '-' ? false : val === 'Unlimited' ? 'Unlimited' : 'Limited to 8'
    ),
  },
  ACTIVITY_YOURS_GET: {
    featureName: 'ACTIVITY_YOURS_GET',
    value: '3',
    Component: NormalComponent,
  },
  ACTIVITY_TEAM_MEMBER_GET: {
    featureName: 'ACTIVITY_TEAM_MEMBER_GET',
    value: '3',
    Component: NormalComponent,
  },
  ACTIVITY_HEAD_GET: {
    featureName: 'ACTIVITY_HEAD_GET',
    value: '3',
    Component: NormalComponent,
  },

  COLLABORATION_PROJECT: {
    featureName: 'COLLABORATION_PROJECT',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  COLLABORATION_JOB: {
    featureName: 'COLLABORATION_JOB',
    value: 'Unlimited',
    Component: NormalComponent,
  },
  SUPPORT: {
    featureName: 'SUPPORT',
    value: 'Unlimited',
    Component: NormalComponent,
  },
} as const;

export const PlanFeatureNormalizer = ({
  featureName,
  value,
}: BEPlan['features'][number]): Plan['features'][number] & {
  hide?: boolean;
} => {
  const Render =
    components[featureName as keyof typeof components]?.Component ||
    NormalComponent;

  return {
    Render: Render(value),
    label: featureName,
    description: featureName,
    hide: components[featureName as keyof typeof components]?.hide,
  };
};

export const PlanNormalizer = ({
  active,
  features,
  planName,
  price,
}: BEPlan): Plan => ({
  ...plans[planName as keyof typeof plans],
  price,
  isActive: active,
  features: features.map(PlanFeatureNormalizer)?.filter(({ hide }) => !hide),
});

export const ActivePlanNormalizer = (data: BEPlan[]): BEPlan[] => {
  let hasActivePlan = false;

  return data
    ?.reverse()
    ?.map((item) => {
      if (hasActivePlan) return { ...item, active: false };
      if (item.active) hasActivePlan = true;

      return item;
    })
    ?.reverse();
};



export const billingNormalizer = (data: BEBilling) => ({
  ...plans[data.planName as keyof typeof plans],
  ...data,
});
