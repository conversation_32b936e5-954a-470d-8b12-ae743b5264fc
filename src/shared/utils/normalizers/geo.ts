import { type PaginateResponse } from '@shared/types/response';
import type { ValueLabelType } from '@shared/types/general';
import type { ILocation } from 'shared/types/lookup';

const searchPlace = (data: any): Array<ILocation> => {
  const uniqueItems = data?.content?.filter(
    (item, index, self) =>
      index === self.findIndex((i) => i.title === item.title)
  );

  return uniqueItems
    ?.slice(0, 10)
    ?.map(
      ({ title, id, countryCode, cityCode, lat, lon, city, ...rest }: any) => ({
        ...rest,
        countryCode,
        title,
        value: cityCode || countryCode,
        label: title,
        lat,
        lon,
        position: {
          lat,
          lon,
        },
        externalId: id,
        city,
        cityName: city,
      })
    );
};
const searchLocation = (data: any): Array<ValueLabelType> =>
  data?.slice(0, 10)?.map(({ title, countryCode }: any) => ({
    value: countryCode,
    label: title,
  }));

const searchCountry = (data: any): Array<ValueLabelType> =>
  data?.slice(0, 10)?.map(({ name, codeIso3 }: any) => ({
    value: codeIso3,
    label: name,
  }));

interface BESearchResponse {
  id: string;
  name: string;
  code: string;
}
const updatedSearchCountry = (
  data: PaginateResponse<BESearchResponse>
): ValueLabelType[] =>
  data?.content
    ?.slice(0, 6)
    ?.map(({ id, name, code }) => ({ id, label: name, value: code }));

const searchCity = (data: any): Array<ValueLabelType> =>
  data?.slice(0, 10)?.map(({ name, title, id }: any) => ({
    value: id,
    label: name || title,
  }));

const updatedSearchCity = (
  data: PaginateResponse<ILocation>
): ValueLabelType[] =>
  data?.content?.slice(0, 6)?.map(({ id, cityName, cityCode }) => ({
    id,
    label: cityName,
    value: cityCode,
  }));

const searchTimezone = (data: any): Array<ValueLabelType<string>> =>
  data?.slice(0, 10)?.map(({ label, id, offset, code }: any) => ({
    value: id,
    label,
    offset,
    code,
  }));

const geoNormalizer = {
  searchLocation,
  searchTimezone,
  searchCity,
  searchPlace,
  searchCountry,
  updatedSearchCountry,
  updatedSearchCity,
};

export default geoNormalizer;
