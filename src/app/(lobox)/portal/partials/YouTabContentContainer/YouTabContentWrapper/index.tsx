import SearchFilterIcon from '@shared/components/molecules/SearchFilterIcon';
import Section, {
  ObjectSectionContainerProps,
} from '@shared/components/molecules/Section';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Flex from '@shared/uikit/Flex';
import SearchInput from '@shared/uikit/SearchInput/Search.component';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React, { useMemo } from 'react';
import { useYouContext } from '../../YouContext';
import SearchList from '@shared/components/Organism/SearchList';
import { RowItems } from './RowItem';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';
import { YouTabkeys } from '@shared/types/you';
import SwitchView from 'app/(lobox)/portal/SwitchView';
import SkeletonWrapper from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentWrapper/SkeletonWrapper';
import dynamic from 'next/dynamic';

const SearchFiltersModal = dynamic(
  () => import('@shared/components/Organism/SearchFiltersModal'),
  { ssr: false }
);

interface YouTabContentWrapperProps<T> {
  component: (item: T) => React.ReactNode;
  emptySectionMessage: string;
  onSearchChanged?: (text: string) => void;
  showFilters?: () => void;
  sectionTitle: string;
  sectionProps?: ObjectSectionContainerProps;
  groups?: object[];
  rootName?: YouTabkeys;
  list: T[];
  isLoading: boolean;
  totalPages: number;
  setPage: (page: number) => void;
}

export default function YouTabContentWrapper<T>({
  component,
  emptySectionMessage,
  onSearchChanged,
  showFilters,
  sectionTitle,
  sectionProps,
  groups,
  rootName,
  list,
  isLoading,
  totalPages,
  setPage,
}: YouTabContentWrapperProps<T>) {
  const { hide, isSearchFilterFormOpen } = useYouFilters();
  const { t } = useTranslation();
  const { selectedTab } = useYouContext();

  const _data = useMemo<T[][]>(
    () =>
      (list
        ?.map((item, index) =>
          index % 3 === 0 ? [item, list[index + 1], list[index + 2]] : false
        )
        ?.filter((item) => item) || []) as T[][],
    [list]
  );

  if (_data?.length === 0 && !isLoading) {
    return (
      <EmptySectionInModules
        title={emptySectionMessage}
        classNames={{ container: 'h-full mx-20 mt-20' }}
      />
    );
  }

  return (
    <>
      <Section
        className="h-auto flex-1 bg-popOverBg px-20 rounded-lg mt-20 mx-20"
        {...sectionProps}
      >
        {rootName &&
          selectedTab === rootName &&
          groups &&
          isSearchFilterFormOpen && (
            <SearchFiltersModal
              onClose={hide}
              groups={groups ?? []}
              isFullWidth={
                rootName === 'candidates' || rootName === 'applicants'
              }
            />
          )}
        <SearchList
          ItemSkeleton={() =>
            SkeletonWrapper({
              rootName: rootName as YouTabkeys,
            })
          }
          title={sectionTitle}
          hasSubTitle={false}
          data={list}
          isLoading={isLoading}
          renderItem={(item, index) => (
            <RowItems
              key={`row_item_${index}`}
              index={index}
              component={component}
              _data={_data}
            />
          )}
          className={{
            wrapper: '!p-0 !m-0 !my-20',
          }}
          headerElement={
            <Flex flexDir="row" className="gap-10">
              <SearchInput
                size="small"
                onChange={(text: string) => onSearchChanged?.(text)}
                placeholder={t('search')}
                className="w-[250px] my-auto"
              />
              {rootName && groups && (
                <SearchFilterIcon
                  size="xl40"
                  variant="rectangle"
                  name="sliders-simple-light"
                  type="far"
                  onClick={showFilters}
                />
              )}
              {/* <SwitchView /> */}
            </Flex>
          }
          totalPages={totalPages}
          onPageChange={(page) => {
            setPage(page);
          }}
          scrollToTopWhenClick={false}
        />
      </Section>
    </>
  );
}
