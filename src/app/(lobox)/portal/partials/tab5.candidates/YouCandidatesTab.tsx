import { CandidateApplicantParticipationNormalizedModel } from '@shared/types/jobsProps';
import React from 'react';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { YouCandidateApplicantCard } from 'app/(lobox)/portal/partials/YouCandidateApplicantCard';
import { useParams } from 'next/navigation';
import { useCandidatesFilterGroups } from '@shared/hooks/searchFilters/useCandidatesFilterGroups';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';

export default function YouCandidatesTab() {
  const { t } = useTranslation();
  const params = useParams();
  const groups = useCandidatesFilterGroups();
  const { show: showFilters } = useYouFilters();

  return (
    <YouTabContentContainer<CandidateApplicantParticipationNormalizedModel>
      sectionTitle={t('candidates')}
      entity="searchParticipationAsCandidate"
      extraParams={{ onlyInvolved: true, teamMemberId: params?.teamMemberId }}
      component={(item) => (
        <YouCandidateApplicantCard item={item} variant="candidate" />
      )}
      emptySectionMessage={t('no_candidates_found')}
      rootName="candidates"
      groups={groups}
      showFilters={showFilters}
    />
  );
}
