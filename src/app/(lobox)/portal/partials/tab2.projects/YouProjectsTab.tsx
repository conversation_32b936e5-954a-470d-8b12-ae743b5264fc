import RecruiterProjectDetailsCardMoreOptions from 'app/(lobox)/search/my-projects/partials/RecruiterProjects/RecruiterProjectDetailsWrapper/RecruiterProjectDetails/RecruiterProjectDetailsCard/RecruiterProjectDetailsCardMoreOptions';
import BaseProjectCardInList from '@shared/components/molecules/ProjectCard/BaseProjectCardInList';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import { ProjectProps } from '@shared/types/project';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React, { useCallback } from 'react';
import YouTabContentContainer from 'app/(lobox)/portal/partials/YouTabContentContainer/YouTabContentContainerWithFilters';
import { useYouFilters } from 'app/(lobox)/portal/partials/YouFilters/useYouFilters';
import { useRecruiterProjectsFilterGroups } from '@shared/hooks/searchFilters/useRecruiterProjectsFilterGroups';
import useHistory from '@shared/utils/hooks/useHistory';
import { routeNames } from '@shared/utils/constants';
import { useParams } from 'next/navigation';

export default function YouProjectsTab() {
  const router = useHistory();
  const { t } = useTranslation();
  const { show: showFilters } = useYouFilters();
  const groups = useRecruiterProjectsFilterGroups();
  const params = useParams();

  const handleAddJob = useCallback(
    (project: ProjectProps) =>
      openMultiStepForm({
        formName: 'linkJobForm',
        data: {
          id: project.id,
          target: 'project',
          cardProps: {
            title: project.title,
            text: project.pageInfo?.title,
            icon: 'projects-light',
            jobs: project.jobs,
          },
          initialJobs: project.jobs?.map((job) => ({ id: job.id })) ?? [],
        },
      }),
    []
  );

  return (
    <YouTabContentContainer<ProjectProps>
      entity="recruiterProjects"
      extraParams={{ onlyInvolved: true, teamMemberId: params?.teamMemberId }}
      sectionTitle={t('projects')}
      component={(item) => (
        <BaseProjectCardInList
          {...item}
          assignees={item?.collaborators}
          user={item?.owner}
          cardProps={{
            classNames: {
              container: 'border border-solid border-techGray_20 rounded-xl',
            },
          }}
          showAssignees
          actions={
            <Flex className="!flex-row gap-12">
              <Button
                label={t('view_details')}
                schema="semi-transparent"
                fullWidth
                to={`${routeNames.searchRecruiterProjects}?currentEntityId=${item.id}`}
              />
              <Button
                label={t('link_jobs')}
                leftIcon="link-rotate"
                leftType="far"
                fullWidth
                onClick={() => handleAddJob(item)}
              />
            </Flex>
          }
          moreOptions={
            <RecruiterProjectDetailsCardMoreOptions
              project={{
                ...item,
                collaboratorUsers: item?.collaborators,
                user: item?.creator,
              }}
            />
          }
        />
      )}
      emptySectionMessage={t('no_projects_found')}
      showFilters={showFilters}
      groups={groups}
      rootName="projects"
    />
  );
}
