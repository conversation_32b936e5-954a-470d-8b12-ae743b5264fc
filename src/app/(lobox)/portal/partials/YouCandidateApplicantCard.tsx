import CandidateCard from '@shared/components/molecules/CandidateCard';
import CardBadges from '@shared/components/molecules/CardItem/CardBadges';
import TextWithIcon from '@shared/components/molecules/TextWithIcon';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import {
  CandidateApplicantParticipationNormalizedModel,
  JobAPIProps,
} from '@shared/types/jobsProps';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import { searchCandidateItem } from '@shared/utils/api/candidates';
import { routeNames } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import React from 'react';

const showJobsTitle = (jobs: JobAPIProps[]) => {
  const { t } = useTranslation();
  if (jobs.length === 0) return '-';
  if (jobs.length === 1) return jobs[0].title;
  return `${jobs?.length} ${t('jobs')}`;
};

export const YouCandidateApplicantCard = ({
  item,
  variant,
}: {
  item: CandidateApplicantParticipationNormalizedModel;
  variant: 'applicant' | 'candidate';
}) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const history = useHistory();

  const openAppliedJobModal = () => {
    appDispatch({
      type: 'TOGGLE_APPLIED_JOB_MODAL',
      payload: {
        open: true,
        data: { jobs: item.jobs },
      },
    });
  };

  const redirectToJob = `${routeNames.searchRecruiterJobs}?currentEntityId=${item?.jobs?.[0]?.id}`;

  const toggleCandidateManager = () => {
    appDispatch({
      type: 'TOGGLE_CANDIDATE_MANAGER',
      payload: {
        isOpen: true,
        entityId: variant === 'applicant' ? item.userId : item.candidateId,
        tab: 'notes',
        enablePagination: false,
        currentIndex: -1,
        apiFunc: searchCandidateItem,
      },
    });
  };

  return (
    <CandidateCard
      avatar={item?.croppedImageUrl}
      firstText={item?.fullName}
      secondText={item?.usernameAtSign}
      thirdText={item?.occupation?.label}
      fourthText={cleanRepeatedWords(item?.location?.title || '')}
      // treeDotMenu={<CandidateCardActions candidate={item?.candidate} />}
      classNames={{
        root: '!border !border-solid !border-techGray_20 !rounded-xl',
      }}
      avatarProps={{ name: item?.fullName }}
    >
      <CardBadges
        notesCount={item?.notesCount}
        todosCount={item?.todosCount}
        meetingsCount={item?.meetingsCount}
        documentsCount={item?.documentsCount}
        reviewsCount={item?.reviewsCount}
        matchingExpectationsCount={item?.skillBoardCount}
      />
      <Flex flexDir="row" alignItems="center" className="gap-4">
        <TextWithIcon
          icon="briefcase-blank-light"
          text={`${t('applied_to')}:`}
          textProps={{
            color: 'muteMidGray',
            className: '!ml-0',
            size: 14,
          }}
          iconProps={{ color: 'muteMidGray', size: 16 }}
        />

        <Button
          variant="text"
          labelColor="brand"
          label={showJobsTitle(item?.jobs)}
          labelClassName="font-bold"
          onClick={item?.jobs?.length > 1 ? openAppliedJobModal : undefined}
          to={item?.jobs?.length === 1 ? redirectToJob : undefined}
        />
      </Flex>
      <Button
        label={t('manage')}
        fullWidth
        onClick={toggleCandidateManager}
        schema="semi-transparent"
      />
    </CandidateCard>
  );
};
