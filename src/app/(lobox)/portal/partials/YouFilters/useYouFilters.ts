import { useMemo } from 'react';
import {
  useSearchDispatch,
  useSearchState,
} from '@shared/contexts/search/search.provider';
import useDynamicFilters from '@shared/hooks/useDynamicFilters';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';

export function useYouFilters() {
  const searchDispatch = useSearchDispatch();
  const { resetFilters } = useSearchFilters();
  const isSearchFilterFormOpen = useSearchState('isSearchFilterFormOpen');
  const dynamicFilters = useDynamicFilters();

  return useMemo(
    () => ({
      show: () => {
        searchDispatch({
          type: 'SET_IS_SEARCH_FILTER_FORM_OPEN',
          payload: true,
        });
      },
      hide: () => {
        searchDispatch({
          type: 'SET_IS_SEARCH_FILTER_FORM_OPEN',
          payload: false,
        });
      },
      dynamicFilters,
      isSearchFilterForm<PERSON>pen,
      resetFilters,
    }),
    [dynamicFilters, isSearchFilterFormOpen]
  );
}
