'use client';
import useGetRole from '@shared/hooks/useGetRole';
import Button from '@shared/uikit/Button';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import { getUser } from '@shared/utils/api/user';
import { QueryKeys, routeNames } from '@shared/utils/constants';
import { PAGE_ROLES } from '@shared/utils/constants/enums';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { YouContainer } from 'app/(lobox)/portal/You';
import { useParams } from 'next/navigation';
import React from 'react';

export default function UserPortalPage() {
  const { t } = useTranslation();
  const params = useParams();
  const { roles } = useGetRole();

  const { data: user, isLoading: isLoadingUser } = useReactQuery({
    action: {
      apiFunc: getUser,
      key: [QueryKeys.getUser, params?.teamMemberId],
      params: {
        containsCroppedHeaderImageLink: true,
        userId: params?.teamMemberId,
      },
    },
    config: {
      enabled: Boolean(params?.teamMemberId),
    },
  });

  const hasSettingsAccess = [
    PAGE_ROLES.OWNER.value,
    PAGE_ROLES.ADMIN.value,
  ].some((role) => roles.includes(role));

  return (
    <>
      <ModalHeaderSimple
        backButtonProps={{
          to: routeNames.portalProfile,
        }}
        title={user?.fullName}
        noCloseButton
        hideBack={false}
        rightContent={() =>
          hasSettingsAccess && (
            <Button
              schema="semi-transparent"
              label={t('edit')}
              leftIcon="pen"
              className="ml-auto"
              to={routeNames.settingsPageRoles}
            />
          )
        }
      />
      <YouContainer isUserPortal user={user} isLoading={isLoadingUser} />;
    </>
  );
}
