import { useQuery } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import { useEffect, useState, type FC } from 'react';
import JobFullDetailsSkeleton from '@shared/components/molecules/Job/JobFullDetailsSkeleton';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { BusinessJobCardSkeleton } from 'shared/components/molecules/BusinessJobCard';
import JobFullDetails from 'shared/components/molecules/Job';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import Flex from 'shared/uikit/Flex';
import { QueryKeys } from 'shared/utils/constants';
import { jobsEndpoints } from 'shared/utils/constants/servicesEndpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import request from 'shared/utils/toolkit/request';
import RecruiterJobDetailsCard from './RecruiterJobDetails/RecruiterJobDetailsCard';
import RecruiterJobDetailsCollaboratorsSkeleton from './RecruiterJobDetails/RecruiterJobDetailsCollaborators/RecruiterJobDetailsCollaboratorsSkeleton';
import RecruiterJobDetailsInsights from './RecruiterJobDetails/RecruiterJobDetailsInsights';
import RecruiterJobDetailsSkeleton from './RecruiterJobDetails/RecruiterJobDetailsSkeleton';
import classes from './RecruiterJobDetails.module.scss';
import type { SingleJobAPIProps } from 'shared/types/jobsProps';

const RecruiterJobDetailsApplicants = dynamic(
  () => import('./RecruiterJobDetails/RecruiterJobDetailsApplicants'),
  {
    loading: () => <RecruiterJobDetailsSkeleton />,
  }
);
const RecruiterJobDetailsCandidates = dynamic(
  () => import('./RecruiterJobDetails/RecruiterJobDetailsCandidates'),
  {
    loading: () => <RecruiterJobDetailsSkeleton />,
  }
);
const RecruiterJobDetailsCollaborators = dynamic(
  () => import('./RecruiterJobDetails/RecruiterJobDetailsCollaborators'),
  {
    loading: () => <RecruiterJobDetailsCollaboratorsSkeleton />,
  }
);
const RecruiterJobDetailsReviews = dynamic(
  () => import('./RecruiterJobDetails/RecruiterJobDetailsReviews'),
  {
    loading: () => <RecruiterJobDetailsSkeleton />,
  }
);
const RecruiterJobDetailsActivities = dynamic(
  () => import('./RecruiterJobDetails/RecruiterJobDetailsActivities'),
  {
    loading: () => <RecruiterJobDetailsSkeleton />,
  }
);

type RecruiterJobDetailsTabsProps =
  | 'about'
  | 'applicants'
  | 'candidates'
  | 'company'
  | 'collaborators'
  | 'reviews'
  | 'assignees'
  | 'activities'
  | 'insights';

interface RecruiterJobDetailsProps {
  jobId?: string | null;
  parentLoading?: boolean;
}

const RecruiterJobDetails: FC<RecruiterJobDetailsProps> = ({
  jobId,
  parentLoading,
}) => {
  const { t } = useTranslation();
  const { allParams } = useCustomParams();
  const { currentEntityId } = allParams;
  const [selectedTab, setSelectedTab] =
    useState<RecruiterJobDetailsTabsProps>('about');
  const { data, isLoading, refetch } = useQuery({
    queryKey: [QueryKeys.jobDetails, jobId],
    queryFn: (params) =>
      request.get<SingleJobAPIProps>(
        jobsEndpoints.getBusinessJobDetails(params.queryKey[1] ?? ''),
        {
          params: {
            containsLastActivity: true,
          },
        }
      ),

    enabled: !!jobId,
  });

  const job = data?.data;

  useEffect(() => {
    const defaultSelectedTab = () => {
      setSelectedTab(
        (window.location?.hash?.replace(
          '#',
          ''
        ) as RecruiterJobDetailsTabsProps) || 'about'
      );
    };

    defaultSelectedTab();
  }, []);

  if (isLoading || parentLoading || !currentEntityId)
    return (
      <Flex>
        <BusinessJobCardSkeleton showTags isMain />
        <JobFullDetailsSkeleton />
      </Flex>
    );

  if (!job)
    return (
      <Flex className="h-full">
        <EmptySearchResult
          className="!m-0"
          sectionMessage={t('no_job_found')}
        />
      </Flex>
    );

  return (
    <Flex className={classes.detailsRoot}>
      <RecruiterJobDetailsCard
        job={job}
        onChangeTab={(tab) =>
          setSelectedTab(tab as RecruiterJobDetailsTabsProps)
        }
        selectedTab={selectedTab}
        refetchJobData={refetch}
      />
      <Panels job={job} tab={selectedTab} />
    </Flex>
  );
};

export default RecruiterJobDetails;

const Panels = ({
  tab,
  job,
}: {
  tab: RecruiterJobDetailsTabsProps;
  job: SingleJobAPIProps;
}) => {
  switch (tab) {
    case 'applicants': {
      return <RecruiterJobDetailsApplicants job={job} />;
    }
    case 'candidates': {
      return <RecruiterJobDetailsCandidates job={job} />;
    }
    case 'collaborators': {
      return <RecruiterJobDetailsCollaborators jobId={job.id} />;
    }
    case 'assignees': {
      return <RecruiterJobDetailsCollaborators jobId={job.id} />;
    }
    case 'reviews': {
      return <RecruiterJobDetailsReviews jobId={job.id} />;
    }
    case 'activities': {
      return <RecruiterJobDetailsActivities jobId={job.id} />;
    }
    case 'insights': {
      return <RecruiterJobDetailsInsights />;
    }
    default: {
      return <JobFullDetails job={job} />;
    }
  }
};
