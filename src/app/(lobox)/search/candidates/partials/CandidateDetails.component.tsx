import { useQueryClient } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import React, { useCallback, useEffect, useState, type FC } from 'react';
import ActivityItem from '@shared/components/molecules/ActivityItem';
import { CandidateCardActions } from '@shared/components/molecules/CandidateCard';
import CandidateCard from '@shared/components/molecules/CandidateCard/CandidateCard';
import CardBadge from '@shared/components/molecules/CardBadge';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import Tabs from '@shared/components/Organism/Tabs';
import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import Button from '@shared/uikit/Button';
import DateView from '@shared/uikit/DateView';
import Flex from '@shared/uikit/Flex/index';
import Tooltip from '@shared/uikit/Tooltip';
import {
  editCandidateAdditionalInfo,
  getCandidateById,
  searchCandidateItem,
} from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import EntityActivitiesSkeleton from 'shared/components/Organism/EntityActivities/EntityActivities.skeleton';
import classes from './CandidateDetails.module.scss';
import CandidateDetailsSkeleton from './CandidateDetails.skeleton';
import CandidateAboutSkeleton from './tab1.CandidateAbout/CandidateAbout.skeleton';
import CandidateJobsSkeleton from './tab2.CandidateJobs/CandidateJobs.skeleton';
import CandidateAvailabilityTab from './tab3.CandidateAvailability';
import SimilarCandidatesSkeleton from './tab4.CandidateSimilar/CandidateSimilar.skeleton';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type {
  BaseCandidateSectionProp,
  CandidateFormData,
} from '@shared/types/candidates';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import { searchGroupTypes } from '@shared/constants/search';

const CandidateAboutTab = dynamic(() => import('./tab1.CandidateAbout'), {
  loading: () => <CandidateAboutSkeleton />,
  ssr: false,
});

const CandidateJobsTab = dynamic(() => import('./tab2.CandidateJobs'), {
  ssr: false,
  loading: () => <CandidateJobsSkeleton />,
});

const CandidateSimilarTab = dynamic(() => import('./tab4.CandidateSimilar'), {
  loading: () => <SimilarCandidatesSkeleton />,
  ssr: false,
});
const EntityActivities = dynamic(
  () => import('shared/components/Organism/EntityActivities'),
  { loading: () => <EntityActivitiesSkeleton />, ssr: false }
);
const CandidateInsightsTab = dynamic(() => import('./tab6.CandidateInsights'));

type CandidateDetailsTabsNames =
  | 'about'
  | 'jobs'
  | 'availability'
  | 'similar'
  | 'activities'
  | 'insights';

interface CandidateDetailsProps {
  candidateId?: string | null;
  parentLoading?: boolean;
  hasCurrentEntityId?: boolean;
  currentIndex?: number;
  totalElements?: number;
}

const tabs: Array<{ path: CandidateDetailsTabsNames; title: string }> = [
  {
    path: 'about',
    title: 'about',
  },
  {
    path: 'jobs',
    title: 'jobs',
  },
  {
    path: 'availability',
    title: 'availability',
  },
  {
    path: 'similar',
    title: 'similar',
  },
  {
    path: 'activities',
    title: 'activities',
  },
  {
    path: 'insights',
    title: 'insights',
  },
];

const CandidateDetails: FC<CandidateDetailsProps> = ({
  candidateId,
  parentLoading,
  currentIndex,
  totalElements,
  hasCurrentEntityId,
}) => {
  const { t } = useTranslation();
  const { allParams } = useCustomParams();
  const { currentEntityId, ...restOfSearchFilters } = allParams;
  const searchGroupType = !isBusinessApp
    ? allParams.searchGroupType || searchGroupTypes.ALL
    : undefined;

  const defaultActiveTab = useGlobalState('entityDetailActiveTab');
  const appDispatch = useGlobalDispatch();
  const [selectedTab, setSelectedTab] =
    useState<CandidateDetailsTabsNames>('about');

  useEffect(() => {
    setSelectedTab('about');
  }, [candidateId]);

  useEffect(() => {
    if (defaultActiveTab) {
      setSelectedTab(defaultActiveTab);
      appDispatch({
        type: 'SET_ENTITY_ACTIVE_TAB',
        payload: { tab: undefined },
      });
    }
  }, [defaultActiveTab]);

  const { data: candidate, isLoading } = useReactQuery<CandidateFormData>({
    action: {
      apiFunc: getCandidateById,
      key: [QueryKeys.getCandidate, candidateId],
      params: {
        id: candidateId,
        containsLastActivity: true,
      },
      spreadParams: true,
    },
    config: {
      enabled: !!candidateId,
    },
  });

  const handleOpenManager = useCallback(
    (tab?: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          currentIndex,
          tab,
          totalElements,
          entity: 'currentEntityId',
          extraParams: {
            text: decodeURIComponent(allParams.query || ''),
            light: !isBusinessApp,
            ...{
              searchGroupType,
              ...restOfSearchFilters,
              isBusinessApp,
              allowFakePreview: 'true',
              containsLastActivity: true,
              scope:
                allParams.scope === 'ONLY_SAVED_SEARCHES'
                  ? 'ALL'
                  : allParams.scope,
            },
            currentEntityId: currentEntityId,
          },
          apiFunc: searchCandidateItem,
          hasCurrentEntityId,
        },
      });
    },
    [appDispatch, totalElements]
  );

  const queryClient = useQueryClient();

  const refetch = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QueryKeys.getCandidate, candidate?.id],
      exact: false,
    });
  }, [candidate?.id, queryClient]);
  const isLoboxUser = !!candidate?.profile?.username;

  if (isLoading || parentLoading) return <CandidateDetailsSkeleton />;

  if (!candidate) return null;

  return (
    <>
      <CandidateCard
        avatar={candidate?.profile?.croppedImageUrl}
        firstText={candidate?.profile?.fullName}
        secondText={candidate?.profile?.usernameAtSign}
        thirdText={candidate.profile?.occupation?.label}
        fourthText={cleanRepeatedWords(
          candidate?.profile?.location?.title || ''
        )}
        treeDotMenu={<CandidateCardActions candidate={candidate} />}
        FirstTextWrapper={!isLoboxUser ? IsManualWrapper : undefined}
        footer={
          <Tabs
            activePath={selectedTab}
            onChangeTab={setSelectedTab}
            styles={{
              tabsRoot: classes.tabsRoot,
            }}
            tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
          />
        }
      >
        <HorizontalTagList
          tags={candidate.tags}
          title={t('candidate_tags')}
          editable
          onSuccess={refetch}
          apiFunc={(body) =>
            editCandidateAdditionalInfo({ candidateId: candidate.id, body })
          }
        />
        <Flex className={classes.badges}>
          <CardBadge
            value={candidate.notesCount}
            iconsDetails={{ iconName: 'note' }}
            tooltipProps={{
              children: t('notes'),
            }}
            onClick={() => handleOpenManager('notes')}
          />
          <CardBadge
            value={candidate.todosCount}
            iconsDetails={{ iconName: 'checklist' }}
            tooltipProps={{
              children: t('todos'),
            }}
            onClick={() => handleOpenManager('todos')}
          />
          <CardBadge
            value={candidate.meetingsCount}
            iconsDetails={{ iconName: 'meeting' }}
            tooltipProps={{
              children: t('meetings'),
            }}
            onClick={() => handleOpenManager('meetings')}
          />
          {!!candidate.lastModifiedDate && (
            <Flex className="ml-auto">
              <Tooltip
                trigger={
                  <DateView
                    className={classes.counterDate}
                    value={`${candidate.lastModifiedDate}`}
                  />
                }
                placement="top"
                className="!bg-hoverPrimary"
                arrowClassName="before:!bg-hoverPrimary"
                hidden={!candidate?.lastActivity?.id}
              >
                {candidate?.lastActivity?.id && (
                  <Flex className="p-6">
                    <ActivityItem item={candidate?.lastActivity} />
                  </Flex>
                )}
              </Tooltip>
            </Flex>
          )}
        </Flex>
        <Flex className="!flex-row gap-12">
          {candidate.profile?.username ? (
            <SendMessageButton
              className="flex-1"
              // disabled={!candidate.profile?.username}
              disabled
              object={{
                id: candidate.profile.originalId,
                croppedImageUrl: candidate.profile.croppedImageUrl,
                fullName: candidate.profile.fullName,
                username: candidate.profile.username,
                isPage: false,
              }}
              fullWidth
            />
          ) : (
            <Button
              disabled
              className="flex-1"
              schema="semi-transparent"
              leftIcon="envelope"
              leftType="far"
              label={t('message')}
              fullWidth
            />
          )}
          <Button
            className="flex-1"
            label={t('manage')}
            leftIcon="user-cog"
            fullWidth
            onClick={() => handleOpenManager('notes')}
          />
        </Flex>
      </CandidateCard>
      <Panels candidate={candidate} tab={selectedTab} />
    </>
  );
};

export default CandidateDetails;

const Panels = ({
  tab,
  candidate,
}: BaseCandidateSectionProp & {
  tab: CandidateDetailsTabsNames;
}) => {
  switch (tab) {
    case 'jobs': {
      return <CandidateJobsTab candidate={candidate} />;
    }
    case 'availability': {
      return <CandidateAvailabilityTab candidate={candidate} />;
    }
    case 'similar': {
      return <CandidateSimilarTab candidate={candidate} />;
    }
    case 'activities': {
      return (
        <EntityActivities
          queryKey={[QueryKeys.searchCandidateActivities, candidate.id]}
          extraProps={{ candidateId: candidate.id }}
        />
      );
    }
    case 'insights': {
      return <CandidateInsightsTab candidate={candidate} />;
    }
    default: {
      return <CandidateAboutTab candidate={candidate} />;
    }
  }
};
