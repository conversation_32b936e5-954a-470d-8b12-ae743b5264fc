'use client';

import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import { userSettingsSchedulePreferences } from 'shared/components/layouts/SidebarLayout/sidebarLayout.data';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PreferencesSingleItem from 'shared/components/Organism/PreferencesModal/PreferencesSingleItem';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './page.module.scss';
import cnj from '@shared/uikit/utils/cnj';

const SchedulePreferences = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container
        contentWrapperClassName={cnj(classes.content, 'gap-12')}
        title={t('schedule_preferences')}
        contentWrapperClassNameDesktop="gap-12"
      >
        {userSettingsSchedulePreferences.map((item) => (
          <PreferencesSingleItem
            key={`preferences-item-${item.routeName}`}
            {...item}
            classNames={{ container: classes.itemContainer }}
          />
        ))}
      </Container>
    </PermissionsGate>
  );
};
export default SchedulePreferences;
