import { useState } from 'react';
import useResponseToast from '@shared/hooks/useResponseToast';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import {
  // Email template functions
  createEmailTemplate,
  updateEmailTemplate,
  setEmailTemplateDefault,
  removeEmailTemplateDefault,
  deleteEmailTemplate,
  // Meeting template functions
  setDefaultMeetingTemplate,
  deleteDefaultMeetingTemplate,
  // Message template functions
  createMessageTemplate,
  updateMessageTemplate,
  setMessageTemplateDefault,
  removeMessageTemplateDefault,
  // Rejection template functions
  updateRejectionTemplate,
  // Common functions
  toggleDefaultTemplate,
  type CreateEmailTemplateRequest,
  type TemplateCategoryType,
} from '@shared/utils/api/template';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  TemplateFormData,
  NormalizedTemplate,
  UseTemplateActionsOptions,
  DuplicatedText,
} from '@shared/components/Organism/AutomationModal/types/template.types';

// Transform form data to API format
const transformFormDataToApi = (data: TemplateFormData): CreateEmailTemplateRequest => ({
  title: data.title || '',
  subject: data.subject || '',
  message: data.message || '',
  timeDelay: data.timeDelay || '0',
  fileIds: (data.fileIds || []).map((id) => parseInt(id, 10)),
  hasFollowup: data.hasFollowup || false,
  followupTitle: data.followupTitle || '',
  followupMessage: data.followupMessage || '',
  followupPeriod: data.followupPeriod || '',
  followupFileIds: (data.followupFileIds || []).map((id) => parseInt(id, 10)),
});

// Category-specific API function mapping
const getCategoryApiFunctions = (category: TemplateCategoryType) => {
  switch (category) {
    case 'email':
      return {
        create: createEmailTemplate,
        update: updateEmailTemplate,
        delete: deleteEmailTemplate,
        setDefault: setEmailTemplateDefault,
        removeDefault: removeEmailTemplateDefault,
      };
    case 'meeting':
      return {
        create: createEmailTemplate, // Fallback to email template API
        update: updateEmailTemplate, // Fallback to email template API
        delete: deleteEmailTemplate, // Fallback to email template API
        setDefault: setDefaultMeetingTemplate,
        removeDefault: deleteDefaultMeetingTemplate,
      };
    case 'message':
      return {
        create: createMessageTemplate,
        update: updateMessageTemplate,
        delete: deleteEmailTemplate, // Fallback to email template API
        setDefault: setMessageTemplateDefault,
        removeDefault: removeMessageTemplateDefault,
      };
    case 'rejection':
      return {
        create: createEmailTemplate, // Fallback to email template API
        update: updateRejectionTemplate,
        delete: deleteEmailTemplate, // Fallback to email template API
        setDefault: setEmailTemplateDefault, // Fallback to email template API
        removeDefault: removeEmailTemplateDefault, // Fallback to email template API
      };
    default:
      return {
        create: createEmailTemplate,
        update: updateEmailTemplate,
        delete: deleteEmailTemplate,
        setDefault: setEmailTemplateDefault,
        removeDefault: removeEmailTemplateDefault,
      };
  }
};

export interface CategoryTemplateActionsOptions extends UseTemplateActionsOptions {
  category: TemplateCategoryType;
}

export const useCategoryTemplateActions = (
  options: CategoryTemplateActionsOptions,
  duplicatedText?: DuplicatedText
) => {
  const {
    category,
    onTemplateCreated,
    onTemplateUpdated,
    onTemplateDeleted,
    onDefaultChanged,
    confirmDelete = true,
  } = options;

  const { handleSuccess } = useResponseToast();
  const { t } = useTranslation();
  const [isUpdatingDefault, setIsUpdatingDefault] = useState<string | null>(null);

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });

  const apiFunctions = getCategoryApiFunctions(category);

  // Create template mutation
  const { mutate: createTemplateMutation, isPending: isCreatePending } =
    useReactMutation({
      apiFunc: (templateData: CreateEmailTemplateRequest) =>
        apiFunctions.create(templateData),
    });

  // Update template mutation
  const { mutate: updateTemplateMutation, isPending: isUpdatePending } =
    useReactMutation({
      apiFunc: ({
        id,
        templateData,
      }: {
        id: number;
        templateData: CreateEmailTemplateRequest;
      }) => {
        if (category === 'rejection') {
          return updateRejectionTemplate(id, {
            title: templateData.title,
            subject: templateData.subject,
            message: templateData.message,
            fileIds: templateData.fileIds,
          });
        }
        return apiFunctions.update(id, templateData);
      },
    });

  // Delete template mutation
  const { mutate: deleteTemplateMutation, isPending: isDeletePending } =
    useReactMutation({
      apiFunc: (id: number) => apiFunctions.delete(id),
    });

  // Set default mutation
  const { mutate: setDefaultMutation, isPending: isSetDefaultPending } =
    useReactMutation({
      apiFunc: (id: number | string) => {
        if (category === 'meeting') {
          return setDefaultMeetingTemplate(String(id));
        }
        return apiFunctions.setDefault(Number(id));
      },
    });

  // Remove default mutation
  const { mutate: removeDefaultMutation, isPending: isRemoveDefaultPending } =
    useReactMutation({
      apiFunc: (id: number | string) => {
        if (category === 'meeting') {
          return deleteDefaultMeetingTemplate(String(id));
        }
        return apiFunctions.removeDefault(Number(id));
      },
    });

  const isMutating =
    isCreatePending ||
    isUpdatePending ||
    isDeletePending ||
    isSetDefaultPending ||
    isRemoveDefaultPending;

  const createTemplate = (
    data: TemplateFormData,
    handleSuccessCallback?: (result: any) => void
  ) => {
    const apiData = transformFormDataToApi(data);

    createTemplateMutation(apiData, {
      onSuccess: (result: any) => {
        const normalizedTemplate: NormalizedTemplate = {
          id: result?.id?.toString(),
          title: result?.title || '',
          subject: result?.subject || '',
          message: result?.message || '',
          fileIds: (result?.fileIds || [])?.map((id: any) => id?.toString()),
          hasFollowup: result?.hasFollowup || false,
          default: result?.default || false,
        };

        onTemplateCreated?.(normalizedTemplate);
        handleSuccessCallback?.(result);
      },
    });
  };

  const updateTemplate = (
    templateId: number,
    data: TemplateFormData,
    handleSuccessCallback?: () => void
  ) => {
    const apiData = transformFormDataToApi(data);

    updateTemplateMutation(
      { id: templateId, templateData: apiData },
      {
        onSuccess: (result: any) => {
          const normalizedTemplate: NormalizedTemplate = {
            id: result?.id?.toString(),
            title: result?.title || '',
            subject: result?.subject || '',
            message: result?.message || '',
            fileIds: (result?.fileIds || [])?.map((id: any) => id?.toString()),
            hasFollowup: result?.hasFollowup || false,
            default: result?.default || false,
          };

          onTemplateUpdated?.(normalizedTemplate);
          handleSuccessCallback?.();
        },
      }
    );
  };

  const deleteTemplate = (template: NormalizedTemplate) => {
    const handleDelete = () => {
      deleteTemplateMutation(parseInt(template.id, 10), {
        onSuccess: () => {
          onTemplateDeleted?.(template.id);
        },
      });
    };

    if (confirmDelete) {
      openConfirmDialog({
        title: t('delete_template'),
        message: t('delete_template_confirmation'),
        onConfirm: handleDelete,
      });
    } else {
      handleDelete();
    }
  };

  const duplicateTemplate = (template: NormalizedTemplate) => {
    const duplicatedData: TemplateFormData = {
      title: `${template.title} (${t('copy')})`,
      subject: template.subject || '',
      message: template.message || '',
      fileIds: template.fileIds || [],
      hasFollowup: template.hasFollowup || false,
      followupTitle: '',
      followupMessage: '',
      followupPeriod: '',
      followupFileIds: [],
      timeDelay: '0',
    };

    createTemplate(duplicatedData, () => {
      if (duplicatedText) {
        handleSuccess(duplicatedText);
      }
    });
  };

  const setDefaultTemplate = (templateId: string, isCurrentlyDefault: boolean) => {
    setIsUpdatingDefault(templateId);

    const mutation = isCurrentlyDefault ? removeDefaultMutation : setDefaultMutation;
    
    mutation(templateId, {
      onSuccess: () => {
        onDefaultChanged?.(templateId, !isCurrentlyDefault);
        setIsUpdatingDefault(null);
      },
      onError: () => {
        setIsUpdatingDefault(null);
      },
    });
  };

  return {
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    setDefaultTemplate,
    isUpdatingDefault,
    isMutating,
  };
};
