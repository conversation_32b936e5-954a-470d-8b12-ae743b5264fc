'use client';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import EnhancedTemplateForm from '@shared/components/Organism/AutomationModal/components/TemplateForm/EnhancedTemplateForm';
import TemplateList from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateList';
import { useTemplateForm } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateForm';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import {
  isValidTemplateCategoryType,
  type TemplateCategoryType,
} from '@shared/utils/api/template';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useCategoryTemplateActions } from './hooks/useCategoryTemplateActions';
import { useCategoryTemplateList } from './hooks/useCategoryTemplateList';
import type {
  NormalizedTemplate,
  TemplateAction,
} from '@shared/components/Organism/AutomationModal/types/template.types';

function getPageDataFromParams(category: TemplateCategoryType) {
  return { title: category };
}

export default function Page({ params }: { params: { category: string } }) {
  const router = useRouter();
  const { t } = useTranslation();
  const category = params?.category;
  const [showForm, setShowForm] = useState(false);

  // Initialize templateForm hook
  const templateForm = useTemplateForm({
    onFormChange: (_values: any, _isValid: boolean) => {},
    onSubmit: (data: any, isCreate?: boolean) => {
      if (isCreate) {
        templateActions.createTemplate(data);
      } else if (templateForm.editingTemplate) {
        templateActions.updateTemplate(
          parseInt(templateForm.editingTemplate.id || '0', 10),
          data,
          () => {
            setShowForm(false);
          }
        );
      }
    },
  });

  const templateList = useCategoryTemplateList({
    category: category as TemplateCategoryType,
    searchEnabled: true,
    onSuccess: (template) => {
      if (template?.length) {
        // templateForm.startEditing(
        //   template?.find(
        //     (item) => item?.id === templateForm?.editingTemplate?.id
        //   ) as unknown as NormalizedTemplate
        // );
      }
    },
  });

  const templateActions = useCategoryTemplateActions(
    {
      category: category as TemplateCategoryType,
      onTemplateCreated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateUpdated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateDeleted: () => {
        templateList.refetch();
      },
      onDefaultChanged: (templateId: string, isCurrentlyDefault: boolean) => {
        templateList.refetch();
      },
    },
    {
      message: t('template_duplicated_message'),
      title: t('template_duplicated_title'),
    }
  );

  if (!isValidTemplateCategoryType(category)) {
    router.replace('/404');

    return null;
  }

  const templateActionsList: TemplateAction[] = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.duplicateTemplate(template);
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template: NormalizedTemplate) => {
        templateForm.startEditing(template);
        setShowForm(true);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.deleteTemplate(template);
      },
    },
  ];

  const handleCreate = () => {
    templateForm.startCreating();
    setShowForm(true);
  };

  const handleTemplateClick = (templateId: string) => {
    const template = templateList.getTemplateById(templateId);
    if (template) {
      templateForm.startEditing(template);
      setShowForm(true);
    }
  };

  const handleFormDiscard = () => {
    setShowForm(false);
    templateForm.resetForm();
  };

  const handleSetDefault = (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    templateActions.setDefaultTemplate(templateId, isCurrentlyDefault);
  };

  const { title } = getPageDataFromParams(category);

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container
        title={
          showForm
            ? templateForm.isEditing
              ? t('edit_template')
              : t('create_template')
            : t(title)
        }
        className="overflow-hidden"
        contentWrapperClassNameDesktop="overflow-hidden"
      >
        <ModalBody className="!p-20 h-full overflow-auto mb-8">
          {showForm ? (
            <EnhancedTemplateForm
              isDefaultTemplate={false}
              onSetDefault={() => {}}
              isUpdatingDefault={false}
              formData={templateForm.formData}
              onSubmit={templateForm.handleSubmit}
              isLoading={templateForm.isLoading}
              onChange={templateForm.handleFormChange}
              config={{
                showDelay: true,
                showFollowup: category === 'email',
                showAttachments: true,
              }}
            />
          ) : (
            <TemplateList
              templates={templateList.templates}
              isLoading={templateList.isLoading}
              searchQuery={templateList.searchQuery}
              onSearchChange={templateList.handleSearchChange}
              onTemplateClick={handleTemplateClick}
              onSetDefault={handleSetDefault}
              actions={templateActionsList}
              isUpdatingDefault={templateActions.isUpdatingDefault}
              config={{
                showSearch: true,
                showActions: true,
                showDefaultToggle: true,
              }}
            />
          )}
        </ModalBody>

        <ModalFooter>
          {showForm ? (
            <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
              <Button
                label={t('discard')}
                schema="gray"
                variant="default"
                onClick={handleFormDiscard}
                className="flex-1"
              />
              <Button
                label={templateForm.isEditing ? t('update') : t('create')}
                schema="primary-blue"
                variant="default"
                onClick={() => templateForm.handleSubmit()}
                className="flex-1"
                disabled={!templateForm.canSubmit}
                isLoading={templateForm.isLoading || templateActions.isMutating}
              />
            </Flex>
          ) : (
            <Button
              fullWidth
              label={t('create_template')}
              leftIcon="plus"
              leftType="fas"
              schema="semi-transparent"
              variant="default"
              onClick={handleCreate}
            />
          )}
        </ModalFooter>
      </Container>
    </PermissionsGate>
  );
}
