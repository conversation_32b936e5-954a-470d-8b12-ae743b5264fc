'use client';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import TemplateList from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateList';
import Button from '@shared/uikit/Button';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import {
  isValidTemplateCategoryType,
  type TemplateCategoryType,
} from '@shared/utils/api/template';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useCategoryTemplateActions } from './hooks/useCategoryTemplateActions';
import { useCategoryTemplateList } from './hooks/useCategoryTemplateList';
import type {
  NormalizedTemplate,
  TemplateAction,
} from '@shared/components/Organism/AutomationModal/types/template.types';
import { routeNames } from '@shared/utils/constants';

function getPageDataFromParams(category: TemplateCategoryType) {
  return { title: category };
}

export default function Page({ params }: { params: { category: string } }) {
  const router = useRouter();
  const { t } = useTranslation();
  const category = params?.category;
  const [showForm, setShowForm] = useState(false);

  const templateList = useCategoryTemplateList({
    category: category as TemplateCategoryType,
    searchEnabled: true,
    onSuccess: (template) => {
      if (template?.length) {
        // templateForm.startEditing(
        //   template?.find(
        //     (item) => item?.id === templateForm?.editingTemplate?.id
        //   ) as unknown as NormalizedTemplate
        // );
      }
    },
  });

  const templateActions = useCategoryTemplateActions(
    {
      category: category as TemplateCategoryType,
      onTemplateCreated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateUpdated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateDeleted: () => {
        templateList.refetch();
      },
      onDefaultChanged: (templateId, isCurrentlyDefault) => {
        templateList.refetch();
      },
    },
    {
      message: t('template_duplicated_message'),
      title: t('template_duplicated_title'),
    }
  );

  if (!isValidTemplateCategoryType(category)) {
    router.replace('/404');

    return null;
  }

  const templateActionsList: TemplateAction[] = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.duplicateTemplate(template);
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template: NormalizedTemplate) => {
        setShowForm(true);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.deleteTemplate(template);
      },
    },
  ];

  const handleCreate = () => {
    router.push(routeNames.settingsTextTemplates.makeCrEditRoute(category));
  };

  const handleTemplateClick = (templateId: string) => {
    const template = templateList.getTemplateById(templateId);
    router.push(
      routeNames.settingsTextTemplates.makeCrEditRoute(category, templateId)
    );
  };

  const handleSetDefault = (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    templateActions.setDefaultTemplate(templateId, isCurrentlyDefault);
  };

  const { title } = getPageDataFromParams(category);

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container
        title={t('create_template')}
        className="overflow-hidden"
        contentWrapperClassNameDesktop="overflow-hidden"
      >
        <ModalBody className="!p-20 h-full overflow-auto mb-8">
          <TemplateList
            templates={templateList.templates}
            isLoading={templateList.isLoading}
            searchQuery={templateList.searchQuery}
            onSearchChange={templateList.handleSearchChange}
            onTemplateClick={handleTemplateClick}
            onSetDefault={handleSetDefault}
            actions={templateActionsList}
            isUpdatingDefault={templateActions.isUpdatingDefault}
            config={{
              showSearch: true,
              showActions: true,
              showDefaultToggle: true,
            }}
          />
        </ModalBody>

        <ModalFooter>
          <Button
            fullWidth
            label={t('create_template')}
            leftIcon="plus"
            leftType="fas"
            schema="semi-transparent"
            variant="default"
            onClick={handleCreate}
          />
        </ModalFooter>
      </Container>
    </PermissionsGate>
  );
}
