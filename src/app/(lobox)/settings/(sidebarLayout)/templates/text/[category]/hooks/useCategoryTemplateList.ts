import { useState, useMemo, useCallback } from 'react';
import {
  getEmailTemplates,
  getAllMeetingTemplates,
  getMessageTemplates,
  searchRejectionTemplates,
  type TemplateCategoryType,
  type EmailTemplateResponse,
  type BETemplateResponse,
} from '@shared/utils/api/template';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type {
  NormalizedTemplate,
  UseTemplateListOptions,
} from '@shared/components/Organism/AutomationModal/types/template.types';
import type { PaginateResponse } from '@shared/types/response';

// Transform template data to normalized format
const defaultTransformTemplate = (template: any): NormalizedTemplate => ({
  id: template?.id?.toString() || '',
  title: template?.title || '',
  subject: template?.subject || '',
  message: template?.message || '',
  fileIds: (template?.fileIds || [])?.map((id: any) => id?.toString()),
  hasFollowup: template?.hasFollowup || false,
  default: template?.default || false,
});

// Category-specific API function mapping
const getCategoryApiFunction = (category: TemplateCategoryType) => {
  switch (category) {
    case 'email':
      return getEmailTemplates;
    case 'meeting':
      return getAllMeetingTemplates;
    case 'message':
      return getMessageTemplates;
    case 'rejection':
      return searchRejectionTemplates;
    default:
      return getEmailTemplates;
  }
};

// Category-specific query key mapping
const getCategoryQueryKey = (category: TemplateCategoryType, searchParams: any) => {
  switch (category) {
    case 'email':
      return [QueryKeys.getEmailTemplates, searchParams];
    case 'meeting':
      return [QueryKeys.getAllMeetingTemplates, searchParams];
    case 'message':
      return [QueryKeys.getMessageTemplates, searchParams];
    case 'rejection':
      return [QueryKeys.searchRejectionTemplates, searchParams];
    default:
      return [QueryKeys.getEmailTemplates, searchParams];
  }
};

export interface CategoryTemplateListOptions extends UseTemplateListOptions {
  category: TemplateCategoryType;
}

export const useCategoryTemplateList = (options: CategoryTemplateListOptions) => {
  const {
    category,
    searchEnabled = true,
    defaultSearchQuery = '',
    transformTemplate = defaultTransformTemplate,
    onSuccess,
  } = options;

  const [searchQuery, setSearchQuery] = useState(defaultSearchQuery);
  const [searchParams, setSearchParams] = useState({
    text: defaultSearchQuery,
    page: 0,
    size: 20,
  });

  const apiFunction = getCategoryApiFunction(category);
  const queryKey = getCategoryQueryKey(category, searchParams);

  const {
    data: apiTemplates,
    isLoading,
    refetch,
  } = useReactQuery<PaginateResponse<EmailTemplateResponse | BETemplateResponse>>({
    action: {
      key: queryKey,
      apiFunc: () => apiFunction(searchParams),
    },
    config: {
      onSuccess: (data) => {
        onSuccess?.(data?.content || []);
      },
    },
  });

  const templates = useMemo(() => {
    if (!apiTemplates?.content) return [];
    return apiTemplates.content.map(transformTemplate);
  }, [apiTemplates?.content, transformTemplate]);

  const filteredTemplates = useMemo(() => {
    if (!searchEnabled || !searchQuery.trim()) return templates;
    
    const query = searchQuery.toLowerCase();
    return templates.filter(
      (template) =>
        template.title?.toLowerCase().includes(query) ||
        template.subject?.toLowerCase().includes(query) ||
        template.message?.toLowerCase().includes(query)
    );
  }, [templates, searchQuery, searchEnabled]);

  const handleSearchChange = useCallback(
    (query: string) => {
      setSearchQuery(query);
      setSearchParams((prev) => ({
        ...prev,
        text: query,
        page: 0,
      }));
    },
    []
  );

  const getTemplateById = useCallback(
    (templateId: string): NormalizedTemplate | undefined => {
      return templates.find((template) => template.id === templateId);
    },
    [templates]
  );

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchParams((prev) => ({
      ...prev,
      text: '',
      page: 0,
    }));
  }, []);

  return {
    templates: filteredTemplates,
    allTemplates: templates,
    isLoading,
    searchQuery,
    hasResults: filteredTemplates.length > 0,
    totalCount: templates.length,
    filteredCount: filteredTemplates.length,
    totalElements: apiTemplates?.totalElements || 0,
    totalPages: apiTemplates?.totalPages || 0,
    refetch,
    handleSearchChange,
    clearSearch,
    getTemplateById,
    isFiltered: searchQuery.trim().length > 0,
  };
};
