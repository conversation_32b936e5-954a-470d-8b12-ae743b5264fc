'use client';

import React from 'react';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import { pageSettingsTemplates } from 'shared/components/layouts/SidebarLayout/sidebarLayout.data';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PreferencesSingleItem from 'shared/components/Organism/PreferencesModal/PreferencesSingleItem';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './page.module.scss';

const Templates = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container
        title={t('Templates')}
        hideBack
        contentWrapperClassName="gap-12"
        contentWrapperClassNameDesktop="gap-12"
      >
        {pageSettingsTemplates.map((item) => (
          <PreferencesSingleItem
            key={`preferences-item-${item.routeName}`}
            {...item}
            classNames={{ container: classes.itemContainer }}
          />
        ))}
      </Container>
    </PermissionsGate>
  );
};

export default Templates;
